#ifndef _STD_ATSMAFX_H__
#define _STD_ATSMAFX_H__
#pragma once
#define WIN32_LEAN_AND_MEAN
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <cstdlib>
#include <queue>
#include <filesystem>
#include <condition_variable>
#include "AXCommon.h"
#include "AtsMidUtility.h"
#include "AtsResource.h"

#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/sinks/basic_file_sink.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include "spdlog/sinks/daily_file_sink.h"

static auto logger = spdlog::daily_logger_st("main_logger", "logs/app.log", 2, 30);

void* fast_memchr(void* haystack, int n, size_t len);
void* sse2_memchr(void* haystack, int n, size_t len);


#define LOG4CXX_TRACE(logger, message) do { \
	logger->trace(message);\
} while (0)


#endif
