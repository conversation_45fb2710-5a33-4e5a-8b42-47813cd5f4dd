@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ============================================================================
REM SQLCompare Cython Multi-Architecture Builder
REM ============================================================================
title SQLCompare Cython Multi-Architecture Builder

echo ============================================================================
echo  SQLCompare Cython Multi-Architecture Builder
echo  Builds Cython extensions for both x64 and x86 architectures
echo ============================================================================
echo.

REM ============================================================================
REM Check Prerequisites
REM ============================================================================
echo [1/4] Checking prerequisites...

REM Check if Visual Studio Build Tools is available
set "VCVARSALL_PATH="
set "VS_YEARS=2022 2019 2017"
set "VS_EDITIONS=BuildTools Community Professional Enterprise"
for %%r in ("%ProgramFiles(x86)%\Microsoft Visual Studio" "%ProgramFiles%\Microsoft Visual Studio") do (
    if exist "%%~r" (
        for %%y in (%VS_YEARS%) do (
            for %%e in (%VS_EDITIONS%) do (
                set "TRY_PATH=%%~r\%%y\%%e\VC\Auxiliary\Build\vcvarsall.bat"
                if exist "!TRY_PATH!" (
                    set "VCVARSALL_PATH=!TRY_PATH!"
                    goto found_vcvarsall
                )
            )
        )
    )
)

:found_vcvarsall
if not defined VCVARSALL_PATH (
    echo ERROR: Could not find vcvarsall.bat.
    echo Please install Visual Studio Build Tools with C++ support.
    pause
    exit /b 1
)

echo Found Visual Studio Build Tools: "%VCVARSALL_PATH%"

REM Check Python installations
echo.
echo Checking for Python installations...

REM Try to find Python installations
set "PYTHON_X64="
set "PYTHON_X86="

REM Check common Python installation paths
for %%p in ("python", "py") do (
    %%p -c "import sys; exit(0 if sys.maxsize > 2**32 else 1)" >nul 2>&1
    if !errorlevel! equ 0 (
        set "PYTHON_X64=%%p"
        echo Found x64 Python: %%p
    ) else (
        %%p -c "import sys; exit(0 if sys.maxsize <= 2**32 else 1)" >nul 2>&1
        if !errorlevel! equ 0 (
            set "PYTHON_X86=%%p"
            echo Found x86 Python: %%p
        )
    )
)

REM ============================================================================
REM Build for x64 Architecture
REM ============================================================================
if defined PYTHON_X64 (
    echo.
    echo [2/4] Building for x64 architecture...
    echo ============================================================================
    
    REM Setup x64 environment
    call "%VCVARSALL_PATH%" x64
    if !errorlevel! neq 0 (
        echo ERROR: Failed to setup x64 build environment
        goto build_x86
    )
    
    REM Set environment variables
    set "DISTUTILS_USE_SDK=1"
    set "MSSdk=1"
    set "PLATFORM_TARGET=x64"
    
    REM Install dependencies for x64
    echo Installing dependencies for x64...
    %PYTHON_X64% -m pip install cython numpy --upgrade
    
    REM Build x64 extension
    echo Building x64 Cython extension...
    %PYTHON_X64% setup_cython.py build_ext --inplace
    if !errorlevel! equ 0 (
        echo SUCCESS: x64 build completed
        set "X64_SUCCESS=1"
    ) else (
        echo ERROR: x64 build failed
        set "X64_SUCCESS=0"
    )
) else (
    echo No x64 Python installation found, skipping x64 build.
    set "X64_SUCCESS=0"
)

:build_x86
REM ============================================================================
REM Build for x86 Architecture
REM ============================================================================
if defined PYTHON_X86 (
    echo.
    echo [3/4] Building for x86 architecture...
    echo ============================================================================
    
    REM Setup x86 environment
    call "%VCVARSALL_PATH%" x86
    if !errorlevel! neq 0 (
        echo ERROR: Failed to setup x86 build environment
        goto summary
    )
    
    REM Set environment variables
    set "DISTUTILS_USE_SDK=1"
    set "MSSdk=1"
    set "PLATFORM_TARGET=x86"
    
    REM Install dependencies for x86
    echo Installing dependencies for x86...
    %PYTHON_X86% -m pip install cython numpy --upgrade
    
    REM Build x86 extension
    echo Building x86 Cython extension...
    %PYTHON_X86% setup_cython.py build_ext --inplace
    if !errorlevel! equ 0 (
        echo SUCCESS: x86 build completed
        set "X86_SUCCESS=1"
    ) else (
        echo ERROR: x86 build failed
        set "X86_SUCCESS=0"
    )
) else (
    echo No x86 Python installation found, skipping x86 build.
    set "X86_SUCCESS=0"
)

:summary
REM ============================================================================
REM Build Summary
REM ============================================================================
echo.
echo [4/4] Build Summary
echo ============================================================================

if "%X64_SUCCESS%"=="1" (
    echo ? x64 build: SUCCESS
) else (
    echo ? x64 build: FAILED or SKIPPED
)

if "%X86_SUCCESS%"=="1" (
    echo ? x86 build: SUCCESS
) else (
    echo ? x86 build: FAILED or SKIPPED
)

echo.
if "%X64_SUCCESS%"=="1" (
    if "%X86_SUCCESS%"=="1" (
        echo ? Both architectures built successfully!
        echo You can now use Cython optimization with both x64 and x86 Python.
    ) else (
        echo ??  Only x64 build succeeded.
    )
) else (
    if "%X86_SUCCESS%"=="1" (
        echo ??  Only x86 build succeeded.
    ) else (
        echo ? Both builds failed. Please check the error messages above.
    )
)

echo.
echo Usage:
echo   from sqlcompare.main_v4 import ComparisonTaskOrchestrator
echo   orchestrator = ComparisonTaskOrchestrator(use_cython=True)
echo.
pause
