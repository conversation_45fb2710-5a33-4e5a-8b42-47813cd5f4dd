#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import tkinter as tk
from pathlib import Path
from tkinter import ttk, messagebox, filedialog
from components.datasource_config_page import DatasourceConfigPage
from components.output_config_page import OutputConfigPage
from components.comparison_rules_page import ComparisonRulesPage
from components.tables_overview_page import TablesOverviewPage
from components.table_comparison_page import TableComparisonPage
from utils.config_manager import SmartConfigManager


class CombinedReporter:
    """组合报告器，将结果同时发送给多个报告器"""

    def __init__(self, reporters):
        self.reporters = reporters

    def open(self):
        for reporter in self.reporters:
            if hasattr(reporter, 'open'):
                reporter.open()

    def close(self):
        for reporter in self.reporters:
            if hasattr(reporter, 'close'):
                reporter.close()

    def report_diff(self, diff_result):
        for reporter in self.reporters:
            if hasattr(reporter, 'report_diff'):
                reporter.report_diff(diff_result)

    def report_match(self, key, value_a=None, value_b=None):
        for reporter in self.reporters:
            if hasattr(reporter, 'report_match'):
                reporter.report_match(key, value_a, value_b)

    def __enter__(self):
        self.open()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class SqlCompareApp:
    """SqlCompare主GUI应用程序"""
    
    def __init__(self):
        """初始化应用程序"""
        # 初始化基本属性
        self._init_attributes()
        
        # 创建主窗口
        self._create_main_window()
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建GUI组件
        self._create_widgets()
        
        # 绑定事件
        self._bind_events()
        
        # 初始化界面状态
        self._init_interface_state()

    
    def _init_attributes(self):
        """初始化基本属性"""
        # 应用程序状态
        self.comparison_running = False
        self.current_config_file = None
        self.config_changed = False

        # GUI组件引用
        self.root = None
        self.main_vertical_paned = None
        self.main_horizontal_paned = None
        self.navigation_tree = None
        self.log_text = None
        self.work_area_frame = None
        self.current_page = None
        self.progress_bar = None
        self.status_label = None

        # 数据存储
        self.table_data = []
        self.comparison_results = []
        self.config_data = {}
        self.config_manager = SmartConfigManager()

    def _safe_call(self, method_name, *args, **kwargs):
        """安全调用方法，避免初始化时的AttributeError"""
        try:
            if hasattr(self, method_name):
                method = getattr(self, method_name)
                if callable(method):
                    return method(*args, **kwargs)
                else:
                    self._log_message(f"方法 {method_name} 不可调用", "WARNING")
            else:
                self._log_message(f"方法 {method_name} 不存在", "WARNING")
        except Exception as e:
            self._log_message(f"调用方法 {method_name} 失败: {e}", "ERROR")

    def _create_main_window(self):
        """创建主窗口"""
        self.root = tk.Tk()
        self.root.title("SqlCompare 数据比对工具")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        
        # 居中显示窗口
        self._center_window()
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
    
    def _center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = 1400
        height = 900
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.configure(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件(F)", menu=file_menu, underline=2)
        file_menu.add_command(label="新建配置(N)", command=self.new_config, 
                             accelerator="Ctrl+N", underline=4)
        file_menu.add_command(label="打开配置(O)", command=self.open_config, 
                             accelerator="Ctrl+O", underline=4)
        file_menu.add_command(label="保存配置(S)", command=self.save_config, 
                             accelerator="Ctrl+S", underline=4)
        file_menu.add_command(label="另存为(A)", command=self.save_config_as, 
                             accelerator="Ctrl+Shift+S", underline=2)
        file_menu.add_separator()
        file_menu.add_command(label="导入模板(I)", command=self.import_template, underline=4)
        file_menu.add_command(label="导出配置(E)", command=self.export_config, underline=4)
        file_menu.add_separator()
        file_menu.add_command(label="最近文件(R)", command=self.show_recent_files, underline=4)
        file_menu.add_separator()
        file_menu.add_command(label="退出(X)", command=self.exit_application, 
                             accelerator="Alt+F4", underline=2)
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑(E)", menu=edit_menu, underline=2)
        edit_menu.add_command(label="撤销(U)", command=self.undo_action, 
                             accelerator="Ctrl+Z", underline=2)
        edit_menu.add_command(label="重做(R)", command=self.redo_action, 
                             accelerator="Ctrl+Y", underline=2)
        edit_menu.add_separator()
        edit_menu.add_command(label="复制(C)", command=self.copy_selection, 
                             accelerator="Ctrl+C", underline=2)
        edit_menu.add_command(label="粘贴(P)", command=self.paste_content, 
                             accelerator="Ctrl+V", underline=2)
        edit_menu.add_separator()
        edit_menu.add_command(label="查找(F)", command=self.find_content, 
                             accelerator="Ctrl+F", underline=2)
        edit_menu.add_command(label="替换(H)", command=self.replace_content, 
                             accelerator="Ctrl+H", underline=2)
        edit_menu.add_separator()
        edit_menu.add_command(label="全选(A)", command=self.select_all, 
                             accelerator="Ctrl+A", underline=2)
        edit_menu.add_separator()
        edit_menu.add_command(label="首选项(P)", command=self.show_preferences, underline=2)
        
        # 运行菜单
        run_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="运行(R)", menu=run_menu, underline=2)
        run_menu.add_command(label="开始比对(S)", command=lambda: self._safe_call('start_comparison'),
                            accelerator="F9", underline=4)
        run_menu.add_command(label="停止比对(T)", command=lambda: self._safe_call('stop_comparison'),
                            accelerator="Shift+F9", underline=2)
        run_menu.add_command(label="暂停比对(P)", command=lambda: self._safe_call('pause_comparison'),
                            accelerator="F8", underline=2)
        run_menu.add_separator()
        run_menu.add_command(label="重新运行(R)", command=lambda: self._safe_call('restart_comparison'),
                            accelerator="Ctrl+F9", underline=2)
        run_menu.add_command(label="清空结果(C)", command=lambda: self._safe_call('clear_results'),
                            accelerator="Ctrl+L", underline=4)
        run_menu.add_separator()
        run_menu.add_command(label="验证配置(V)", command=lambda: self._safe_call('validate_config'), underline=2)
        run_menu.add_command(label="测试连接(T)", command=lambda: self._safe_call('test_connections'), underline=2)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具(T)", menu=tools_menu, underline=2)
        tools_menu.add_command(label="数据源管理(D)", command=self.manage_datasources, underline=4)
        tools_menu.add_command(label="SQL生成器(S)", command=self.sql_generator, underline=0)
        tools_menu.add_command(label="结果导出(E)", command=self.export_results, underline=4)
        tools_menu.add_separator()
        tools_menu.add_command(label="性能监控(P)", command=self.show_performance_monitor, underline=2)
        tools_menu.add_command(label="日志查看器(L)", command=self.show_log_viewer, underline=2)
        tools_menu.add_separator()
        tools_menu.add_command(label="插件管理(M)", command=self.manage_plugins, underline=4)
        tools_menu.add_command(label="系统信息(I)", command=self.show_system_info, underline=4)
        
        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图(V)", menu=view_menu, underline=2)
        view_menu.add_command(label="刷新界面(R)", command=self.refresh_interface, 
                             accelerator="F5", underline=2)
        view_menu.add_command(label="重置布局(L)", command=self.reset_layout, underline=4)
        view_menu.add_separator()
        view_menu.add_command(label="显示工具栏(T)", command=self.toggle_toolbar, underline=4)
        view_menu.add_command(label="显示状态栏(S)", command=self.toggle_statusbar, underline=4)
        view_menu.add_separator()
        view_menu.add_command(label="全屏模式(F)", command=self.toggle_fullscreen, 
                             accelerator="F11", underline=2)
        view_menu.add_command(label="缩放(Z)", command=self.show_zoom_options, underline=2)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助(H)", menu=help_menu, underline=2)
        help_menu.add_command(label="用户手册(U)", command=self.show_user_manual, underline=2)
        help_menu.add_command(label="快速入门(Q)", command=self.show_quick_start, underline=2)
        help_menu.add_command(label="快捷键(K)", command=self.show_shortcuts, underline=2)
        help_menu.add_separator()
        help_menu.add_command(label="检查更新(C)", command=self.check_updates, underline=2)
        help_menu.add_command(label="反馈问题(F)", command=self.report_issue, underline=2)
        help_menu.add_separator()
        help_menu.add_command(label="关于(A)", command=self.show_about, underline=2)
    
    def _create_widgets(self):
        """创建GUI组件"""
        # 创建工具栏 - 直接在根窗口上，无边距
        self._create_toolbar(self.root)

        # 创建主要内容区域 - 有边距
        content_frame = ttk.Frame(self.root)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # 创建主体分割布局（上下分割：主工作区 + 底部信息区）
        self._create_main_layout(content_frame)

        # 创建状态栏 - 直接在根窗口上，无边距
        self._create_status_bar(self.root)
    
    def _create_toolbar(self, parent):
        """创建DBeaver风格的工具栏"""
        # 工具栏主容器 - 无边框
        toolbar_container = tk.Frame(parent, bg='#f0f0f0', height=40)
        toolbar_container.pack(fill=tk.X)
        toolbar_container.pack_propagate(False)  # 防止高度变化

        # 工具栏内容区域
        toolbar = tk.Frame(toolbar_container, bg='#f0f0f0')
        toolbar.pack(fill=tk.BOTH, expand=True)

        # 底部分隔线
        bottom_line = tk.Frame(toolbar_container, height=1, bg='#d0d0d0')
        bottom_line.pack(fill=tk.X, side=tk.BOTTOM)

        # 文件操作按钮组
        self._create_icon_button(toolbar, "📄", "新建", lambda: self._safe_call('new_config'))
        self._create_icon_button(toolbar, "📁", "打开", lambda: self._safe_call('open_config'))
        self._create_icon_button(toolbar, "💾", "保存", lambda: self._safe_call('save_config'))

        # 分隔符
        self._create_toolbar_separator(toolbar)

        # 运行控制按钮组
        self.start_btn = self._create_icon_button(toolbar, "▶", "开始", lambda: self._safe_call('start_comparison'))
        self.stop_btn = self._create_icon_button(toolbar, "⏹", "停止", lambda: self._safe_call('stop_comparison'), state='disabled')
        self._create_icon_button(toolbar, "⏸", "暂停", lambda: self._safe_call('pause_comparison'))

        # 分隔符
        self._create_toolbar_separator(toolbar)

        # 工具按钮组
        self._create_icon_button(toolbar, "🔗", "连接", lambda: self._safe_call('test_connections'))
        self._create_icon_button(toolbar, "✓", "验证", lambda: self._safe_call('validate_config'))
        self._create_icon_button(toolbar, "🗑", "清空", lambda: self._safe_call('clear_results'))
        self._create_icon_button(toolbar, "📤", "导出", lambda: self._safe_call('export_results'))

        # 分隔符
        self._create_toolbar_separator(toolbar)

        # 视图控制按钮组
        self._create_icon_button(toolbar, "🔄", "刷新", lambda: self._safe_call('refresh_interface'))
        self._create_icon_button(toolbar, "⚙", "设置", lambda: self._safe_call('show_preferences'))

        # 右侧区域
        right_frame = tk.Frame(toolbar, bg='#f0f0f0')
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # 帮助按钮
        self._create_icon_button(right_frame, "❓", "帮助", lambda: self._safe_call('show_user_manual'))

    def _create_icon_button(self, parent, icon, text, command, state='normal'):
        """创建DBeaver风格的图标按钮"""
        # 按钮容器 - 调整边距，紧贴工具栏边缘
        btn_frame = tk.Frame(parent, bg='#f0f0f0')
        btn_frame.pack(side=tk.LEFT, padx=1, pady=2)

        # 创建按钮
        btn = tk.Button(btn_frame,
                       text=f"{icon}\n{text}",
                       command=command,
                       state=state,
                       font=('Segoe UI', 8),
                       bg='#f0f0f0',
                       fg='#333333',
                       relief=tk.FLAT,
                       bd=0,
                       padx=6,
                       pady=4,
                       width=6,
                       height=2,
                       activebackground='#e0e0e0',
                       cursor='hand2',
                       compound=tk.TOP)
        btn.pack()

        # 悬停效果 - 只改变背景颜色，不显示边框
        def on_enter(event):
            if btn['state'] != 'disabled':
                btn.config(bg='#e0e0e0')

        def on_leave(event):
            if btn['state'] != 'disabled':
                btn.config(bg='#f0f0f0')

        def on_press(event):
            if btn['state'] != 'disabled':
                btn.config(bg='#d0d0d0')  # 按下时更深的颜色

        def on_release(event):
            if btn['state'] != 'disabled':
                # 检查鼠标是否还在按钮上
                x, y = btn.winfo_pointerxy()
                widget = btn.winfo_containing(x, y)
                if widget == btn:
                    btn.config(bg='#e0e0e0')  # 鼠标还在按钮上，保持悬停色
                else:
                    btn.config(bg='#f0f0f0')  # 鼠标不在按钮上，恢复正常色

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        btn.bind("<ButtonPress-1>", on_press)
        btn.bind("<ButtonRelease-1>", on_release)

        return btn

    def _create_toolbar_separator(self, parent):
        """创建工具栏分隔符"""
        sep = tk.Frame(parent, width=1, height=32, bg='#d0d0d0', relief=tk.SUNKEN, bd=1)
        sep.pack(side=tk.LEFT, padx=4, pady=2)

    def _create_main_layout(self, parent):
        """创建主体布局 - 上下分割：主工作区 + 底部信息区"""
        # 创建垂直分割面板（上下分割）
        self.main_vertical_paned = ttk.PanedWindow(parent, orient=tk.VERTICAL)
        self.main_vertical_paned.pack(fill=tk.BOTH, expand=True)

        # 上部：主工作区（左侧导航 + 右侧工作区）- 占3/4高度
        main_work_frame = tk.Frame(self.main_vertical_paned)
        self.main_vertical_paned.add(main_work_frame, weight=3)

        # 下部：底部信息区域 - 占1/4高度
        bottom_info_frame = tk.Frame(self.main_vertical_paned)
        self.main_vertical_paned.add(bottom_info_frame, weight=1)

        # 先创建底部信息区域（包含日志文本框）
        self._create_bottom_info_area(bottom_info_frame)

        # 再创建主工作区的水平分割（左侧导航 + 右侧工作区）
        # 这样导航树初始化时日志功能已可用
        self._create_main_work_area(main_work_frame)

        # 设置初始分割位置 - 确保底部区域只占1/4
        self.root.after(300, self._set_initial_pane_position)

    def _set_initial_pane_position(self):
        """设置初始分割面板位置"""
        try:
            # 强制更新窗口以获取正确的尺寸
            self.root.update_idletasks()

            # 获取窗口高度并设置垂直分割位置
            window_height = self.root.winfo_height()
            if window_height > 200:
                # 底部区域占1/4，主工作区占3/4
                bottom_height = max(150, window_height // 4)
                main_height = window_height - bottom_height - 50
                self.main_vertical_paned.sashpos(0, main_height)

            # 获取窗口宽度并设置水平分割位置
            window_width = self.root.winfo_width()
            if window_width > 400:
                # 左侧导航区域固定宽度约250px
                nav_width = max(200, min(300, window_width // 4))
                self.main_horizontal_paned.sashpos(0, nav_width)

        except Exception as e:
            # 使用默认位置
            try:
                self.main_vertical_paned.sashpos(0, 500)
                self.main_horizontal_paned.sashpos(0, 250)
            except:
                pass

    def _set_default_selection(self):
        """设置默认选择项 - 选择比对列表"""
        try:
            # 确保导航树已创建
            if not hasattr(self, 'navigation_tree') or self.navigation_tree is None:
                print("❌ 导航树未创建")
                return

            # 查找比对列表节点并选择
            children = self.navigation_tree.get_children()

            for item in children:
                item_text = self.navigation_tree.item(item, 'text')
                values = self.navigation_tree.item(item, 'values')
                self._log_message(f"检查导航项: '{item_text}', values: {values}", "INFO")

                # 查找比对列表节点（通过values识别）
                if values and values[0] == "tables_category":
                    # 选择比对列表项
                    self.navigation_tree.selection_set(item)
                    self.navigation_tree.focus(item)
                    # 触发选择事件
                    self.on_navigation_tree_selection(None)
                    self._log_message("默认选择比对列表页面", "INFO")
                    return

            self._log_message("未找到比对列表导航项", "WARNING")

        except Exception as e:
            self._log_message(f"设置默认选择项失败: {e}", "WARNING")

    def _create_main_work_area(self, parent):
        """创建主工作区 - 水平分割：左侧导航 + 右侧工作区"""
        # 创建水平分割面板，去除边框
        self.main_horizontal_paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        self.main_horizontal_paned.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # 配置分割面板样式 - 使用系统默认背景色
        style = ttk.Style()
        style.configure("TPanedwindow", borderwidth=0, relief="flat")

        # 左侧：树形导航面板（固定宽度）
        left_frame = tk.Frame(self.main_horizontal_paned)
        left_frame.configure(width=220)
        self.main_horizontal_paned.add(left_frame, weight=0)

        # 右侧：SDI工作区
        right_frame = tk.Frame(self.main_horizontal_paned)
        self.main_horizontal_paned.add(right_frame, weight=1)

        # 设置初始分割位置（左侧220像素）
        self.main_horizontal_paned.after(1, lambda: self.main_horizontal_paned.sashpos(0, 220))

        # 创建左侧树形导航
        self._create_navigation_tree(left_frame)

        # 创建右侧SDI工作区
        self._create_sdi_work_area(right_frame)

    def _create_navigation_tree(self, parent):
        """创建左侧树形导航面板"""
        # 直接创建树形控件容器，使用系统默认背景色
        tree_frame = tk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 创建树形控件
        self.navigation_tree = ttk.Treeview(tree_frame, show="tree")
        # 创建滚动条，需要时显示
        tree_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.navigation_tree.yview)
        self.navigation_tree.configure(yscrollcommand=self._on_tree_scroll)
        self.tree_scrollbar = tree_scrollbar

        # 配置树形控件样式
        style = ttk.Style()
        style.configure("Treeview", fieldbackground="white", background="white", selectbackground="#0078d4", selectforeground="white")
        # 配置树形控件的行样式
        style.map("Treeview", background=[('selected', '#0078d4')], foreground=[('selected', 'white')])
        # 布局去除内边距
        self.navigation_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=0, pady=0)

        # 初始化树形结构
        self._init_navigation_tree()
        # 绑定选择事件
        self.navigation_tree.bind("<<TreeviewSelect>>", self.on_navigation_tree_selection)
        # 绑定配置事件，用于动态显示滚动条
        self.navigation_tree.bind("<Configure>", self._check_tree_scrollbar)

    def _on_tree_scroll(self, *args):
        """处理树形控件滚动事件"""
        self.tree_scrollbar.set(*args)
        self._check_tree_scrollbar()

    def _check_tree_scrollbar(self, event=None):
        """检查是否需要显示滚动条"""
        try:
            # 获取滚动条的位置信息
            top, bottom = self.navigation_tree.yview()

            # 如果内容完全可见，隐藏滚动条
            if top <= 0.0 and bottom >= 1.0:
                self.tree_scrollbar.pack_forget()
            else:
                # 否则显示滚动条
                self.tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        except:
            pass


    def _init_navigation_tree(self):
        """初始化树形导航结构"""
        # 清空现有内容
        for item in self.navigation_tree.get_children():
            self.navigation_tree.delete(item)

        # 比对配置
        self.navigation_tree.insert("", "end", text="比对配置", values=("comparison_config",))
        # 比对列表
        tables_node = self.navigation_tree.insert("", "end", text="比对列表", open=True, values=("tables_category",))
        # 加载表信息
        self._update_tables_in_navigation_tree(tables_node)

    def _update_tables_in_navigation_tree(self, parent_node):
        """更新导航树中的比对列表"""
        if not self.config_manager.config:
            config_files = self.config_manager.auto_discover_configs()
            if config_files:
                self.config_manager.load_config(config_files[0])

        # 清空现有节点
        for item in self.navigation_tree.get_children(parent_node):
            self.navigation_tree.delete(item)

        try:
            tables = self.config_manager.get_comparison_tables()
            for table in tables:
                table_id = table.get('table_id', '')
                self.navigation_tree.insert(parent_node, "end", text=table_id, values=("table_item", table_id))
        except Exception as e:
            self._log_message(f"导航树：加载表信息失败: {e}", "error")
            self.navigation_tree.insert(parent_node, "end", text="加载失败", values=("load_error",))

    def _create_sdi_work_area(self, parent):
        """创建右侧SDI工作区域 - 使用现代化的边框样式"""
        # 创建外层容器，用于边框效果
        border_frame = tk.Frame(parent, bg="#cccccc")
        border_frame.pack(fill=tk.BOTH, expand=True)

        # 创建工作区容器，内嵌在边框容器中
        self.work_area_frame = tk.Frame(border_frame, bg="white")
        self.work_area_frame.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # 当前显示的页面组件
        self.current_page = None

        # 默认显示整合的比对配置页面
        self.root.after(200, self._set_default_selection)

    def _create_bottom_info_area(self, parent):
        """创建底部信息区域 - 使用现代化的边框样式"""
        # 创建外层容器，用于边框效果
        border_frame = tk.Frame(parent, bg="#cccccc")
        border_frame.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # 创建日志区域容器，内嵌在边框容器中
        log_container = tk.Frame(border_frame, bg="white")
        log_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # 创建日志标题栏
        log_header = tk.Frame(log_container, bg="white")
        log_header.pack(fill=tk.X, padx=5, pady=(5, 0))

        # 日志标题
        log_title = tk.Label(log_header, text="执行日志", font=("Arial", 9, "bold"), bg="white", fg="#333333")
        log_title.pack(side=tk.LEFT)

        # 创建日志文本区域
        log_text_frame = tk.Frame(log_container, bg="white")
        log_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建日志文本控件，去除边框
        self.log_text = tk.Text(log_text_frame, wrap=tk.WORD, font=("Consolas", 9),
                               relief=tk.FLAT, bd=0, highlightthickness=0)

        # 添加滚动条
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        # 布局日志文本和滚动条
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 初始化日志内容
        self.log_text.insert(tk.END, "SQL比对工具已启动\n")
        self.log_text.insert(tk.END, f"启动时间: {self._get_current_time()}\n")
        self.log_text.insert(tk.END, "-" * 50 + "\n")

        # 设置日志文本为只读
        self.log_text.configure(state=tk.DISABLED)

    def _get_current_time(self):
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _show_welcome_page(self):
        """显示欢迎页面"""
        # 清除当前页面
        self._clear_work_area()

        # 创建欢迎页面 - 使用白色背景
        welcome_frame = tk.Frame(self.work_area_frame, bg="white")
        welcome_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建内容容器，添加适当的边距
        content_frame = tk.Frame(welcome_frame, bg="white")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)

        # 欢迎标题
        title_label = tk.Label(content_frame,
                               text="SQL 数据比对工具",
                               font=("Arial", 20, "bold"),
                               bg="white", fg="#333333")
        title_label.pack(pady=(0, 30))

        # 说明文本
        info_text = """请从左侧导航树选择要查看或编辑的内容：

• 比对配置 - 配置数据源连接、输出设置和比对规则
• 比对列表 - 查看所有要比对的表和比对结果

选择具体项目后，相应的配置或结果页面将在此处显示。"""

        info_label = tk.Label(content_frame, text=info_text,
                              font=("Arial", 12), justify=tk.LEFT,
                              bg="white", fg="#666666")
        info_label.pack(anchor=tk.W, pady=20)

        # 快速操作按钮
        button_frame = tk.Frame(content_frame, bg="white")
        button_frame.pack(anchor=tk.W, pady=(30, 0))

        ttk.Button(button_frame, text="配置数据源",
                  command=self._show_datasource_config_page).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="查看比对列表",
                  command=self._show_tables_overview_page).pack(side=tk.LEFT)

        self.current_page = welcome_frame

    def _show_error_page(self, page_name, error_message):
        """显示错误页面"""
        self._clear_work_area()

        # 创建白色背景的错误页面
        error_frame = tk.Frame(self.work_area_frame, bg="white")
        error_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建内容容器
        content_frame = tk.Frame(error_frame, bg="white")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)

        # 错误标题
        title_label = tk.Label(content_frame,
                               text=f"加载 {page_name} 时出错",
                               font=("Arial", 16, "bold"),
                               bg="white", fg="#d32f2f")
        title_label.pack(pady=(0, 20))

        # 错误信息
        error_label = tk.Label(content_frame, text=f"错误详情：{error_message}",
                               font=("Arial", 11), justify=tk.LEFT,
                               bg="white", fg="#666666")
        error_label.pack(anchor=tk.W, pady=10)

        # 建议操作
        suggestion_label = tk.Label(content_frame,
                                    text="建议：请检查相关模块是否正确安装，或联系技术支持。",
                                    font=("Arial", 11),
                                    bg="white", fg="#999999")
        suggestion_label.pack(anchor=tk.W, pady=10)

        # 返回按钮容器
        button_frame = tk.Frame(content_frame, bg="white")
        button_frame.pack(anchor=tk.W, pady=(20, 0))

        ttk.Button(button_frame, text="返回首页",
                  command=self._show_welcome_page).pack()

        self.current_page = error_frame


    def _clear_work_area(self):
        """清除工作区域内容"""
        if self.current_page:
            # 如果是页面组件，调用其destroy方法
            if hasattr(self.current_page, 'destroy'):
                self.current_page.destroy()
            else:
                # 如果是普通Frame，直接销毁
                self.current_page.destroy()
            self.current_page = None

    def on_navigation_tree_selection(self, event):
        """处理导航树选择事件"""
        selection = self.navigation_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.navigation_tree.item(item, 'values')
        if not values:
            return

        item_type = values[0]

        # 根据选择的项目类型显示相应页面
        if item_type == "comparison_config":
            self._show_integrated_config_page()
        elif item_type == "tables_category":
            self._show_tables_overview_page()
        elif item_type == "table_item":
            table_id = values[1] if len(values) > 1 else ""
            self._show_table_comparison_page(table_id)


    # ==================== 页面组件方法 ====================

    def _show_integrated_config_page(self):
        """显示整合的比对配置页面"""
        self._clear_work_area()

        # 创建主配置页面容器
        config_frame = tk.Frame(self.work_area_frame)
        config_frame.pack(fill=tk.BOTH, expand=True)

        # 创建可滚动的内容区域
        canvas = tk.Canvas(config_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)

        def on_frame_configure(event):
            """当内容框架大小改变时更新滚动区域"""
            canvas.configure(scrollregion=canvas.bbox("all"))
            # 检查是否需要滚动条
            self._update_scrollbar_visibility(canvas, scrollbar)

        def on_canvas_configure(event):
            """当画布大小改变时更新内容框架宽度"""
            canvas.itemconfig(canvas_window, width=event.width)

        scrollable_frame.bind("<Configure>", on_frame_configure)
        canvas.bind("<Configure>", on_canvas_configure)

        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 创建紧凑布局的内容容器
        main_container = tk.Frame(scrollable_frame)
        main_container.pack(fill=tk.X, padx=30, pady=20)

        # 创建内容区域，紧凑垂直布局
        content_frame = tk.Frame(main_container)
        content_frame.pack(fill=tk.X)

        # 创建数据源配置区域
        self._create_datasource_config_section(content_frame)

        # 添加适当的间距
        spacer1 = tk.Frame(content_frame, height=15)
        spacer1.pack(fill=tk.X)

        # 创建输出配置区域
        self._create_output_config_section(content_frame)

        # 存储引用以便后续使用
        self.config_canvas = canvas
        self.config_scrollbar = scrollbar
        self.current_page = config_frame

    def _update_scrollbar_visibility(self, canvas, scrollbar):
        """根据内容高度动态显示/隐藏滚动条"""
        try:
            canvas.update_idletasks()
            # 获取画布和内容的高度
            canvas_height = canvas.winfo_height()
            scroll_region = canvas.cget("scrollregion").split()
            if len(scroll_region) >= 4:
                content_height = float(scroll_region[3]) - float(scroll_region[1])

                # 如果内容高度小于等于画布高度，隐藏滚动条
                if content_height <= canvas_height:
                    scrollbar.pack_forget()
                else:
                    scrollbar.pack(side="right", fill="y")
        except Exception as e:
            # 如果出现错误，保持滚动条显示
            pass

    def _create_datasource_config_section(self, parent):
        """创建数据源配置区域 - 紧凑布局"""
        # 创建数据库配置容器
        db_config_group = ttk.LabelFrame(parent, text="数据库连接配置")
        db_config_group.pack(fill=tk.X, pady=(0, 10))

        # 创建主容器，使用Grid布局
        main_container = tk.Frame(db_config_group)
        main_container.pack(fill=tk.X, padx=15, pady=15)

        # 配置Grid权重，使两列等宽
        main_container.grid_columnconfigure(0, weight=1)
        main_container.grid_columnconfigure(1, weight=1)

        # === 左侧：源数据库配置 ===
        source_frame = ttk.LabelFrame(main_container, text="源数据库")
        source_frame.grid(row=0, column=0, sticky="ew", padx=(0, 10), pady=0)

        source_content = tk.Frame(source_frame)
        source_content.pack(fill=tk.X, padx=12, pady=12)

        # 源数据库配置字段
        self._create_db_config_fields(source_content, "DB1")

        # === 右侧：目标数据库配置 ===
        target_frame = ttk.LabelFrame(main_container, text="目标数据库")
        target_frame.grid(row=0, column=1, sticky="ew", padx=(10, 0), pady=0)

        target_content = tk.Frame(target_frame)
        target_content.pack(fill=tk.X, padx=12, pady=12)

        # 目标数据库配置字段
        self._create_db_config_fields(target_content, "DB2")

    def _create_db_config_fields(self, parent, db_section):
        """创建数据库配置字段 - 紧凑布局"""
        # 从配置管理器获取配置值
        config_values = self._get_db_config_values(db_section)

        # 数据库类型
        tk.Label(parent, text="数据库类型:").grid(row=0, column=0, sticky="w", pady=2)
        db_type_combo = ttk.Combobox(parent, values=["DB2", "MySQL", "PostgreSQL", "SQL Server", "Oracle"],
                                    state="readonly", width=18)
        db_type_combo.set(config_values.get("TYPE", "DB2"))
        db_type_combo.grid(row=0, column=1, sticky="ew", pady=2)

        # 主机地址
        tk.Label(parent, text="主机地址:").grid(row=1, column=0, sticky="w", pady=2)
        host_entry = tk.Entry(parent, width=20)
        host_entry.insert(0, config_values.get("IP", ""))
        host_entry.grid(row=1, column=1, sticky="ew", pady=2)

        # 端口
        tk.Label(parent, text="端口:").grid(row=2, column=0, sticky="w", pady=2)
        port_entry = tk.Entry(parent, width=20)
        port_entry.insert(0, config_values.get("PORT", ""))
        port_entry.grid(row=2, column=1, sticky="ew", pady=2)

        # 用户名
        tk.Label(parent, text="用户名:").grid(row=3, column=0, sticky="w", pady=2)
        user_entry = tk.Entry(parent, width=20)
        user_entry.insert(0, config_values.get("USER_NAME", ""))
        user_entry.grid(row=3, column=1, sticky="ew", pady=2)

        # 密码
        tk.Label(parent, text="密码:").grid(row=4, column=0, sticky="w", pady=2)
        password_entry = tk.Entry(parent, show="*", width=20)
        password_entry.insert(0, config_values.get("PASSWORD", ""))
        password_entry.grid(row=4, column=1, sticky="ew", pady=2)

        # 模式/数据库
        tk.Label(parent, text="模式:").grid(row=5, column=0, sticky="w", pady=2)
        schema_entry = tk.Entry(parent, width=20)
        schema_entry.insert(0, config_values.get("SCHEMA", ""))
        schema_entry.grid(row=5, column=1, sticky="ew", pady=2)

        # 测试连接按钮
        test_btn = ttk.Button(parent, text="测试连接")
        test_btn.grid(row=6, column=0, columnspan=2, pady=(8, 0))

        # 配置列权重
        parent.grid_columnconfigure(1, weight=1)

    def _get_db_config_values(self, db_section):
        """从配置管理器获取数据库配置值"""
        config_values = {}
        try:
            if self.config_manager and self.config_manager.config:
                if self.config_manager.config.has_section(db_section):
                    for key, value in self.config_manager.config.items(db_section):
                        config_values[key.upper()] = value
        except Exception as e:
            self._log_message(f"读取{db_section}配置失败: {e}", "WARNING")
        return config_values

    def _create_output_config_section(self, parent):
        """创建输出配置区域 - 紧凑布局"""
        # 输出配置组
        output_group = ttk.LabelFrame(parent, text="输出配置")
        output_group.pack(fill=tk.X)

        # 输出配置内容
        output_content = tk.Frame(output_group)
        output_content.pack(fill=tk.X, padx=15, pady=15)

        # 使用Grid布局优化空间利用
        output_content.grid_columnconfigure(1, weight=1)

        # === 输出格式 ===
        tk.Label(output_content, text="输出格式:").grid(row=0, column=0, sticky="w", pady=(0, 10), padx=(0, 15))
        format_combo = ttk.Combobox(output_content, values=["CSV", "Excel", "JSON", "HTML"],
                                   state="readonly", width=20)
        format_combo.set("CSV")
        format_combo.grid(row=0, column=1, sticky="w", pady=(0, 10))

        # === 输出路径 ===
        tk.Label(output_content, text="输出路径:").grid(row=1, column=0, sticky="w", pady=(0, 10), padx=(0, 15))

        path_frame = tk.Frame(output_content)
        path_frame.grid(row=1, column=1, sticky="ew", pady=(0, 10))
        path_frame.grid_columnconfigure(0, weight=1)

        path_entry = tk.Entry(path_frame)
        path_entry.insert(0, "./output/comparison_result.csv")
        path_entry.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        browse_btn = ttk.Button(path_frame, text="浏览")
        browse_btn.grid(row=0, column=1, sticky="w")

        # === 输出选项 ===
        tk.Label(output_content, text="输出选项:", font=("", 10, "bold")).grid(row=2, column=0, sticky="nw", pady=(0, 5), padx=(0, 15))

        # 输出选项复选框 - 垂直排列，紧凑间距
        checkbox_frame = tk.Frame(output_content)
        checkbox_frame.grid(row=2, column=1, sticky="w", pady=(0, 5))

        include_matched_var = tk.BooleanVar(value=False)
        tk.Checkbutton(checkbox_frame, text="包含匹配记录", variable=include_matched_var).pack(anchor="w", pady=1)

        include_summary_var = tk.BooleanVar(value=True)
        tk.Checkbutton(checkbox_frame, text="包含汇总信息", variable=include_summary_var).pack(anchor="w", pady=1)

        auto_open_var = tk.BooleanVar(value=True)
        tk.Checkbutton(checkbox_frame, text="自动打开结果", variable=auto_open_var).pack(anchor="w", pady=1)



    def _show_datasource_config_page(self):
        """显示数据源配置页面"""
        self._clear_work_area()

        try:
            self.current_page = DatasourceConfigPage(self.work_area_frame, self)
            self.current_page.show()
            self._log_message("显示数据源配置页面（完整组件）")
            return
        except Exception as e:
            self._log_message(f"加载数据源配置页面组件失败: {e}", "ERROR")

    def _show_output_config_page(self):
        """显示输出配置页面"""
        self._clear_work_area()

        try:
            self.current_page = OutputConfigPage(self.work_area_frame, self)
            self.current_page.show()
            self._log_message("显示输出配置页面（完整组件）")
            return
        except Exception as e:
            self._log_message(f"加载输出配置页面组件失败: {e}", "ERROR")

    def _show_comparison_rules_page(self):
        """显示比对规则页面"""
        self._clear_work_area()

        try:
            self.current_page = ComparisonRulesPage(self.work_area_frame, self)
            self.current_page.show()
            self._log_message("显示比对规则配置页面（完整组件）")
            return
        except Exception as e:
            self._log_message(f"加载比对规则配置页面组件失败: {e}", "ERROR")

    def _show_tables_overview_page(self):
        """显示比对列表概览页面"""
        self._clear_work_area()

        try:
            self.current_page = TablesOverviewPage(self.work_area_frame, self)
            self.current_page.show()
            self._log_message("显示比对列表概览页面（完整组件）")
            return
        except Exception as e:
            self._log_message(f"加载比对列表概览页面组件失败: {e}", "ERROR")

    def _show_table_comparison_page(self, table_id):
        """显示单表比对结果页面"""
        self._clear_work_area()

        # table_id现在直接是表名，无需处理
        table_name = table_id

        try:
            self.current_page = TableComparisonPage(self.work_area_frame, self, table_name)
            self.current_page.show()
            self._log_message(f"显示表 {table_name} 比对结果页面")
            return
        except Exception as e:
            self._log_message(f"加载表 {table_name} 比对结果页面组件失败: {e}", "ERROR")

    def _create_status_bar(self, parent):
        """创建状态栏 - 参考第二个截图的样式"""
        # 状态栏主容器
        status_frame = tk.Frame(parent, bg='#f0f0f0', height=25)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)  # 防止高度变化

        # 顶部分隔线
        top_line = tk.Frame(status_frame, height=1, bg='#d0d0d0')
        top_line.pack(fill=tk.X, side=tk.TOP)

        # 状态栏内容区域
        content_frame = tk.Frame(status_frame, bg='#f0f0f0')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)

        # 左侧：操作状态描述
        self.status_label = tk.Label(content_frame, text="就绪",
                                    bg='#f0f0f0', fg='#333333',
                                    font=('Segoe UI', 9), anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 分隔符
        sep1 = tk.Frame(content_frame, width=1, bg='#d0d0d0')
        sep1.pack(side=tk.RIGHT, fill=tk.Y, padx=5)

        # 差异数量统计
        self.diff_count_label = tk.Label(content_frame, text="差异: 0",
                                        bg='#f0f0f0', fg='#333333',
                                        font=('Segoe UI', 9), width=12)
        self.diff_count_label.pack(side=tk.RIGHT)

        # 分隔符
        sep2 = tk.Frame(content_frame, width=1, bg='#d0d0d0')
        sep2.pack(side=tk.RIGHT, fill=tk.Y, padx=5)

        # 比对速度
        self.speed_label = tk.Label(content_frame, text="速度: 0 行/秒",
                                   bg='#f0f0f0', fg='#333333',
                                   font=('Segoe UI', 9), width=15)
        self.speed_label.pack(side=tk.RIGHT)

        # 分隔符
        sep3 = tk.Frame(content_frame, width=1, bg='#d0d0d0')
        sep3.pack(side=tk.RIGHT, fill=tk.Y, padx=5)

        # 进度条
        self.progress_bar = ttk.Progressbar(content_frame, length=150, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, padx=(0, 5))

        # 比对进度描述
        self.progress_desc_label = tk.Label(content_frame, text="",
                                           bg='#f0f0f0', fg='#333333',
                                           font=('Segoe UI', 9), width=20)
        self.progress_desc_label.pack(side=tk.RIGHT)

        # 分隔符
        sep4 = tk.Frame(content_frame, width=1, bg='#d0d0d0')
        sep4.pack(side=tk.RIGHT, fill=tk.Y, padx=5)

        # 时间显示
        self.time_label = tk.Label(content_frame, text="",
                                  bg='#f0f0f0', fg='#333333',
                                  font=('Segoe UI', 9), width=20)
        self.time_label.pack(side=tk.RIGHT)

        # 启动时间更新
        self._update_time()

        # 初始化状态栏数据
        self._init_status_bar_data()

    def _init_status_bar_data(self):
        """初始化状态栏数据"""
        self.comparison_stats = {
            'total_tables': 0,
            'completed_tables': 0,
            'total_differences': 0,
            'current_speed': 0,
            'start_time': None
        }

    def update_status_bar(self, status_text=None, progress_desc=None,
                         progress_value=None, speed=None, diff_count=None):
        """更新状态栏信息"""
        if status_text is not None:
            self.status_label.config(text=status_text)

        if progress_desc is not None:
            self.progress_desc_label.config(text=progress_desc)

        if progress_value is not None:
            self.progress_bar['value'] = progress_value

        if speed is not None:
            self.speed_label.config(text=f"速度: {speed} 行/秒")
            self.comparison_stats['current_speed'] = speed

        if diff_count is not None:
            self.diff_count_label.config(text=f"差异: {diff_count}")
            self.comparison_stats['total_differences'] = diff_count

    def update_comparison_progress(self, completed_tables, total_tables):
        """更新比对进度"""
        self.comparison_stats['completed_tables'] = completed_tables
        self.comparison_stats['total_tables'] = total_tables

        if total_tables > 0:
            progress_percent = (completed_tables / total_tables) * 100
            self.progress_bar['value'] = progress_percent
            self.progress_desc_label.config(text=f"{completed_tables}/{total_tables} 表")
        else:
            self.progress_bar['value'] = 0
            self.progress_desc_label.config(text="")

    def _bind_events(self):
        """绑定事件处理"""
        # 快捷键绑定
        self.root.bind('<Control-n>', lambda e: self._safe_call('new_config'))
        self.root.bind('<Control-o>', lambda e: self._safe_call('open_config'))
        self.root.bind('<Control-s>', lambda e: self._safe_call('save_config'))
        self.root.bind('<Control-Shift-S>', lambda e: self._safe_call('save_config_as'))
        self.root.bind('<Control-z>', lambda e: self._safe_call('undo_action'))
        self.root.bind('<Control-y>', lambda e: self._safe_call('redo_action'))
        self.root.bind('<Control-c>', lambda e: self._safe_call('copy_selection'))
        self.root.bind('<Control-v>', lambda e: self._safe_call('paste_content'))
        self.root.bind('<Control-f>', lambda e: self._safe_call('find_content'))
        self.root.bind('<Control-h>', lambda e: self._safe_call('replace_content'))
        self.root.bind('<Control-a>', lambda e: self._safe_call('select_all'))
        self.root.bind('<F5>', lambda e: self._safe_call('refresh_interface'))
        self.root.bind('<F8>', lambda e: self._safe_call('pause_comparison'))
        self.root.bind('<F9>', lambda e: self._safe_call('start_comparison'))
        self.root.bind('<Shift-F9>', lambda e: self._safe_call('stop_comparison'))
        self.root.bind('<Control-F9>', lambda e: self._safe_call('restart_comparison'))
        self.root.bind('<Control-l>', lambda e: self._safe_call('clear_results'))
        self.root.bind('<F11>', lambda e: self._safe_call('toggle_fullscreen'))
        self.root.bind('<Alt-F4>', lambda e: self._safe_call('exit_application'))

        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", lambda: self._safe_call('exit_application'))

    def _init_interface_state(self):
        """初始化界面状态"""
        # 设置初始状态
        self.stop_btn.config(state='disabled')
        self.progress_bar['value'] = 0

        # 更新状态栏
        self._update_status("就绪")

        # 初始化日志
        self._log_message("界面初始化完成", "INFO")

    def _select_default_navigation_item(self):
        """选择默认的导航项（比对列表）"""
        try:
            self._log_message("开始选择默认导航项", "INFO")

            # 获取所有导航项
            children = self.navigation_tree.get_children()
            self._log_message(f"导航树子项数量: {len(children)}", "INFO")

            # 查找"比对列表"节点
            for item in children:
                item_text = self.navigation_tree.item(item, "text")
                values = self.navigation_tree.item(item, 'values')
                self._log_message(f"检查导航项: '{item_text}', values: {values}", "INFO")

                if values and values[0] == "tables_category":
                    # 选择"比对列表"节点
                    self.navigation_tree.selection_set(item)
                    self.navigation_tree.focus(item)
                    # 手动触发选择事件
                    self._show_tables_overview_page()
                    self._log_message("默认显示比对列表页面", "INFO")
                    return

            # 如果没有找到"比对列表"节点
            self._log_message("未找到'tables_category'导航项", "WARNING")

        except Exception as e:
            self._log_message(f"选择默认导航项失败: {e}", "WARNING")

    # ==================== 文件菜单事件处理 ====================

    def new_config(self):
        """新建配置"""
        if self.config_changed:
            result = messagebox.askyesnocancel("未保存的更改", "当前配置有未保存的更改，是否保存？")
            if result is True:
                self.save_config()
            elif result is None:
                return

        # 清空当前配置
        self.config_data = {}
        self.current_config_file = None
        self.config_changed = False

        # 重新初始化导航树
        self._init_navigation_tree()

        # 默认选择比对列表页面
        self._select_default_navigation_item()

        self._update_status("新建配置完成")
        self._log_message("创建新配置", "INFO")

    def open_config(self):
        """打开配置文件"""
        file_path = filedialog.askopenfilename(
            title="打开配置文件",
            filetypes=[
                ("YAML files", "*.yaml *.yml"),
                ("JSON files", "*.json"),
                ("INI files", "*.ini"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            try:
                # 这里应该加载配置文件的逻辑
                self.current_config_file = file_path
                self.config_changed = False

                # 更新窗口标题
                self.root.title(f"SqlCompare 数据比对工具 - {Path(file_path).name}")

                self._update_status(f"配置文件已加载: {Path(file_path).name}")
                self._log_message(f"加载配置文件: {file_path}", "INFO")

                # 重新加载导航树
                self._init_navigation_tree()

                # 默认选择比对列表页面
                self._select_default_navigation_item()

            except Exception as e:
                messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")
                self._log_message(f"加载配置文件失败: {str(e)}", "ERROR")

    def save_config(self):
        """保存配置文件"""
        if self.current_config_file:
            try:
                # 这里应该保存配置文件的逻辑
                self.config_changed = False
                self._update_status("配置已保存")
                self._log_message("配置文件已保存", "INFO")
            except Exception as e:
                messagebox.showerror("错误", f"保存配置文件失败: {str(e)}")
                self._log_message(f"保存配置文件失败: {str(e)}", "ERROR")
        else:
            self.save_config_as()

    def save_config_as(self):
        """另存为配置文件"""
        file_path = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".yaml",
            filetypes=[
                ("YAML files", "*.yaml"),
                ("JSON files", "*.json"),
                ("INI files", "*.ini")
            ]
        )
        if file_path:
            try:
                # 这里应该保存配置文件的逻辑
                self.current_config_file = file_path
                self.config_changed = False

                # 更新窗口标题
                self.root.title(f"SqlCompare 数据比对工具 - {Path(file_path).name}")

                self._update_status(f"配置已保存: {Path(file_path).name}")
                self._log_message(f"配置文件已保存: {file_path}", "INFO")

            except Exception as e:
                messagebox.showerror("错误", f"保存配置文件失败: {str(e)}")
                self._log_message(f"保存配置文件失败: {str(e)}", "ERROR")

    def import_template(self):
        """导入模板"""
        messagebox.showinfo("功能开发中", "导入模板功能正在开发中...")
        self._log_message("导入模板功能调用", "INFO")

    def export_config(self):
        """导出配置"""
        messagebox.showinfo("功能开发中", "导出配置功能正在开发中...")
        self._log_message("导出配置功能调用", "INFO")

    def show_recent_files(self):
        """显示最近文件"""
        messagebox.showinfo("功能开发中", "最近文件功能正在开发中...")
        self._log_message("最近文件功能调用", "INFO")

    def exit_application(self):
        """退出应用程序"""
        if self.config_changed:
            result = messagebox.askyesnocancel("未保存的更改", "配置有未保存的更改，是否保存？")
            if result is True:
                self.save_config()
            elif result is None:
                return

        if self.comparison_running:
            self.stop_comparison()

        self._log_message("应用程序退出", "INFO")
        self.root.destroy()

    # ==================== 编辑菜单事件处理 ====================

    def undo_action(self):
        """撤销操作"""
        messagebox.showinfo("功能开发中", "撤销功能正在开发中...")
        self._log_message("撤销操作调用", "INFO")

    def redo_action(self):
        """重做操作"""
        messagebox.showinfo("功能开发中", "重做功能正在开发中...")
        self._log_message("重做操作调用", "INFO")

    def copy_selection(self):
        """复制选中内容"""
        try:
            # 获取当前焦点控件
            focused_widget = self.root.focus_get()
            if hasattr(focused_widget, 'selection_get'):
                focused_widget.event_generate("<<Copy>>")
                self._log_message("复制操作完成", "INFO")
        except:
            messagebox.showinfo("提示", "没有可复制的内容")

    def paste_content(self):
        """粘贴内容"""
        try:
            # 获取当前焦点控件
            focused_widget = self.root.focus_get()
            if hasattr(focused_widget, 'insert'):
                focused_widget.event_generate("<<Paste>>")
                self._log_message("粘贴操作完成", "INFO")
        except:
            messagebox.showinfo("提示", "无法粘贴内容")

    def find_content(self):
        """查找内容"""
        messagebox.showinfo("功能开发中", "查找功能正在开发中...")
        self._log_message("查找功能调用", "INFO")

    def replace_content(self):
        """替换内容"""
        messagebox.showinfo("功能开发中", "替换功能正在开发中...")
        self._log_message("替换功能调用", "INFO")

    def select_all(self):
        """全选"""
        try:
            # 获取当前焦点控件
            focused_widget = self.root.focus_get()
            if hasattr(focused_widget, 'select_range'):
                focused_widget.select_range(0, tk.END)
            elif hasattr(focused_widget, 'tag_add'):
                focused_widget.tag_add(tk.SEL, "1.0", tk.END)
            self._log_message("全选操作完成", "INFO")
        except:
            pass

    def show_preferences(self):
        """显示首选项"""
        messagebox.showinfo("功能开发中", "首选项功能正在开发中...")
        self._log_message("首选项功能调用", "INFO")

    # ==================== 比对功能核心方法 ====================
    def _validate_comparison_config(self):
        """验证比对配置"""
        try:
            # 检查配置管理器是否已加载
            if not self.config_manager.config:
                config_files = self.config_manager.auto_discover_configs()
                if not config_files:
                    messagebox.showerror("配置错误", "未找到配置文件，请先配置数据源")
                    self._log_message("未找到配置文件", "ERROR")
                    return False

                if not self.config_manager.load_config(config_files[0]):
                    messagebox.showerror("配置错误", "无法加载配置文件")
                    self._log_message("无法加载配置文件", "ERROR")
                    return False

            # 验证必要的配置节
            required_sections = ['COMMON', 'DB1', 'DB2']
            for section in required_sections:
                if not self.config_manager.config.has_section(section):
                    messagebox.showerror("配置错误", f"配置文件缺少必要的节: {section}")
                    self._log_message(f"配置文件缺少必要的节: {section}", "ERROR")
                    return False

            # 验证比对规则
            if not self.config_manager.rules:
                messagebox.showerror("配置错误", "未找到比对规则文件")
                self._log_message("未找到比对规则文件", "ERROR")
                return False

            tables = self.config_manager.get_comparison_tables()
            if not tables:
                messagebox.showerror("配置错误", "比对规则文件中没有定义任何表")
                self._log_message("比对规则文件中没有定义任何表", "ERROR")
                return False

            self._log_message("配置验证通过", "INFO")
            return True

        except Exception as e:
            messagebox.showerror("配置错误", f"配置验证失败: {str(e)}")
            self._log_message(f"配置验证失败: {str(e)}", "ERROR")
            return False

    def _execute_comparison(self):
        """执行比对任务"""
        import threading

        def comparison_thread():
            try:
                self._run_comparison_task()
            except Exception as e:
                # 在主线程中处理错误
                error_msg = str(e)
                self.root.after(0, lambda msg=error_msg: self._on_comparison_error(msg))
            finally:
                # 在主线程中重置状态
                self.root.after(0, self._on_comparison_finished)

        # 启动后台线程
        thread = threading.Thread(target=comparison_thread, daemon=True)
        thread.start()
        self._log_message("比对任务已在后台启动", "INFO")

    def _run_comparison_task(self):
        """运行比对任务的核心逻辑"""
        try:
            # 导入必要的模块
            from connectors.db2_connector import DB2Connector
            from reporters.csvfile_reporter import CsvFileReporter
            from reporters.gui_reporter import GuiReporter
            from reporters.sqlite_reporter import SqliteReporter
            from core.engine import compare_sources_stream, compare_sources_memory

            # 更新状态
            self.root.after(0, lambda: self.update_status_bar(status_text="正在加载配置...", progress_value=10))
            self.root.after(0, lambda: self._log_message("正在加载配置...", "INFO"))

            # 1. 提取配置信息
            db1_config = dict(self.config_manager.config['DB1'])
            db2_config = dict(self.config_manager.config['DB2'])

            # 获取比对类型配置
            common_config = dict(self.config_manager.config['COMMON'])
            cmp_type = int(common_config.get('CMP_TYPE', 1))

            # 获取比对表列表
            tables = self.config_manager.get_comparison_tables()
            total_tables = len(tables)

            self.root.after(0, lambda: self._log_message(f"找到 {total_tables} 个比对表", "INFO"))
            self.root.after(0, lambda: self.update_status_bar(status_text="正在初始化连接器...", progress_value=20))

            # 2. 处理每个表的比对
            completed_tables = 0
            total_differences = 0

            for table_index, table_rule in enumerate(tables):
                if not self.comparison_running:  # 检查是否被停止
                    break

                table_id = table_rule.get('table_id', f'table_{table_index}')
                sql1 = table_rule.get('sql_1', '').strip()
                sql2 = table_rule.get('sql_2', '').strip()
                table_name = table_rule.get('table_id', '')

                if not sql1 or not sql2:
                    self.root.after(0, lambda tid=table_id: self._log_message(
                        f"跳过表 {tid}: SQL查询为空", "WARNING"))
                    continue

                # 更新进度
                progress = 20 + (table_index / total_tables) * 60
                self.root.after(0, lambda p=progress, tid=table_id: self.update_status_bar(status_text=f"正在比对表: {tid}", progress_value=p))
                self.root.after(0, lambda tid=table_id: self._log_message(f"开始比对表: {tid}", "INFO"))

                # 3. 创建连接器和报告器
                fetch_batch_size = 10000
                source_a = DB2Connector(db1_config, query=sql1, batch_size=fetch_batch_size)
                source_b = DB2Connector(db2_config, query=sql2, batch_size=fetch_batch_size)

                # 创建GUI报告器，支持实时更新
                gui_reporter_config = {
                    'progress_callback': self._on_comparison_progress,
                    'result_callback': lambda results, tid=table_id: self._on_comparison_results(results, tid),
                    'update_interval': 10,  # 10秒更新一次
                    'buffer_size': 10000  # 10000条结果批量更新
                }
                gui_reporter = GuiReporter(config=gui_reporter_config)

                # 创建SQLite报告器，支持实时查询
                sqlite_reporter_config = {
                    'db_path': 'diff_results.db',
                    'table_name': 'comparison_results',
                    'comparison_table': table_id,
                    'batch_size': 20000,  # 增大批量大小提高性能
                    'append_mode': True,  # 使用追加模式避免文件锁定
                    'silent_mode': True,  # 静默模式，减少控制台输出
                    'commit_interval': 100000  # 减少提交频率
                }
                sqlite_reporter = SqliteReporter(config=sqlite_reporter_config)

                # 同时创建CSV报告器保存结果
                csvfile_reporter_config = {'filepath': f'comparison_report_{table_id}.csv', 'buffer_size': 10000}
                csvfile_reporter = CsvFileReporter(config=csvfile_reporter_config)

                # 4. 执行比对
                try:
                    # 打开报告器
                    gui_reporter.open()
                    sqlite_reporter.open()
                    csvfile_reporter.open()

                    if cmp_type == 1:
                        self.root.after(0, lambda: self._log_message("使用流式归并算法", "INFO"))
                        combined_reporter = CombinedReporter([gui_reporter, sqlite_reporter, csvfile_reporter])
                        compare_sources_stream(source_a, source_b, combined_reporter)
                    elif cmp_type == 2:
                        self.root.after(0, lambda: self._log_message("使用内存字典算法", "INFO"))
                        combined_reporter = CombinedReporter([gui_reporter, sqlite_reporter, csvfile_reporter])
                        compare_sources_memory(source_a, source_b, combined_reporter)
                    else:
                        raise ValueError(f"不支持的比对类型: {cmp_type}")

                finally:
                    # 确保报告器被正确关闭
                    try:
                        gui_reporter.close()
                        sqlite_reporter.close()
                        csvfile_reporter.close()
                    except Exception as e:
                        self.root.after(0, lambda: self._log_message(f"关闭报告器时出错: {e}", "WARNING"))

                completed_tables += 1

                # 更新完成状态
                self.root.after(0, lambda ct=completed_tables, tt=total_tables:
                    self.update_comparison_progress(ct, tt))
                self.root.after(0, lambda tid=table_id: self._log_message(
                    f"表 {tid} 比对完成", "INFO"))

            # 5. 完成所有比对
            self.root.after(0, lambda: self.update_status_bar(status_text="比对任务完成", progress_value=100))
            self.root.after(0, lambda ct=completed_tables: self._log_message(f"所有比对任务完成，共处理 {ct} 个表", "INFO"))

        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda msg=error_msg: self._log_message(f"比对任务执行失败: {msg}", "ERROR"))
            raise

    def _on_comparison_progress(self, progress_info):
        """处理比对进度更新"""
        try:
            status = progress_info.get('status', 'running')
            message = progress_info.get('message', '')
            processed = progress_info.get('processed', 0)
            differences = progress_info.get('differences', 0)
            speed = progress_info.get('speed', 0)

            # 生成更详细的状态消息
            if processed > 0:
                if speed > 0:
                    detailed_message = f"已处理 {processed:,} 条记录，速度: {speed:.0f} 条/秒，差异: {differences:,} 条"
                else:
                    detailed_message = f"已处理 {processed:,} 条记录，差异: {differences:,} 条"
            else:
                detailed_message = message or "正在处理数据..."

            # 更新状态栏
            self.update_status_bar(status_text=detailed_message, speed=int(speed), diff_count=differences)
            # 记录详细日志到GUI
            self._log_message(detailed_message, "INFO")

        except Exception as e:
            self._log_message(f"进度更新失败: {str(e)}", "WARNING")

    def _on_comparison_results(self, results, table_id):
        """处理比对结果更新"""
        try:
            # 如果当前显示的是对应表的比对页面，更新结果
            if (hasattr(self, 'current_page') and
                hasattr(self.current_page, '__class__') and
                'TableComparisonPage' in str(self.current_page.__class__) and
                hasattr(self.current_page, 'table_name') and
                self.current_page.table_name == table_id):

                # 转换结果格式
                comparison_data = []
                for i, diff in enumerate(results):
                    status_map = {
                        'SO': '仅在源中存在',
                        'TO': '仅在目标中存在',
                        'DF': '数据不匹配',
                        'FD': '字段差异',
                        'ID': '完全相同',
                        # 兼容旧格式
                        'IN_A_ONLY': '仅在源中存在',
                        'IN_B_ONLY': '仅在目标中存在',
                        'DIFFERENT': '数据不匹配'
                    }

                    comparison_data.append({
                        "record_id": f"{i+1:03d}",
                        "diff_type": status_map.get(diff.status, '未知状态'),
                        "primary_key": diff.key,
                        "source_data": diff.value_a,
                        "target_data": diff.value_b
                    })

                # 更新页面数据
                if hasattr(self.current_page, '_update_comparison_data'):
                    self.current_page._update_comparison_data(comparison_data)

        except Exception as e:
            self._log_message(f"结果更新失败: {str(e)}", "WARNING")

    def _on_comparison_finished(self):
        """比对任务完成后的处理"""
        try:
            self.comparison_running = False
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

            self.update_status_bar(status_text="比对任务已完成")
            self._log_message("比对任务已完成", "INFO")

            # 刷新导航树以显示新的结果
            self._refresh_navigation_tree()

            # 如果当前显示的是表比对页面，刷新结果
            if (hasattr(self, 'current_page') and
                hasattr(self.current_page, '__class__') and
                'TableComparisonPage' in str(self.current_page.__class__)):
                self.current_page.refresh()

        except Exception as e:
            self._log_message(f"比对完成处理失败: {str(e)}", "ERROR")

    def _on_comparison_error(self, error_message):
        """比对任务出错时的处理"""
        try:
            self.comparison_running = False
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

            self.update_status_bar(status_text="比对任务失败", progress_value=0)
            self._log_message(f"比对任务失败: {error_message}", "ERROR")

            messagebox.showerror("比对失败", f"比对任务执行失败:\n{error_message}")

        except Exception as e:
            self._log_message(f"错误处理失败: {str(e)}", "ERROR")

    def _refresh_navigation_tree(self):
        """刷新导航树"""
        try:
            # 重新加载表信息
            for item in self.navigation_tree.get_children():
                item_text = self.navigation_tree.item(item, 'text')
                if item_text == "比对列表":
                    self._update_tables_in_navigation_tree(item)
                    break
        except Exception as e:
            self._log_message(f"刷新导航树失败: {str(e)}", "WARNING")

    # ==================== 运行菜单事件处理 ====================

    def start_comparison(self):
        """开始比对"""
        if self.comparison_running:
            messagebox.showwarning("警告", "比对任务正在运行中")
            return

        try:
            # 验证配置
            if not self._validate_comparison_config():
                return

            # 更新界面状态
            self.comparison_running = True
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')

            # 重置进度
            self.progress_bar['value'] = 0
            self.update_status_bar(status_text="正在启动比对任务...", progress_value=0)

            self._log_message("开始数据比对任务", "INFO")

            # 启动实际的比对逻辑
            self._execute_comparison()

        except Exception as e:
            self.comparison_running = False
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            messagebox.showerror("错误", f"启动比对失败: {str(e)}")
            self._log_message(f"启动比对失败: {str(e)}", "ERROR")

    def stop_comparison(self):
        """停止比对"""
        if not self.comparison_running:
            messagebox.showwarning("警告", "没有正在运行的比对任务")
            return

        try:
            # 设置停止标志
            self.comparison_running = False

            # 更新界面状态
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

            # 更新状态
            self.update_status_bar(status_text="正在停止比对任务...", progress_value=0)
            self._log_message("正在停止比对任务...", "INFO")

            # 注意：实际的线程停止会在后台线程检查comparison_running标志时发生

        except Exception as e:
            messagebox.showerror("错误", f"停止比对失败: {str(e)}")
            self._log_message(f"停止比对失败: {str(e)}", "ERROR")

    def pause_comparison(self):
        """暂停比对"""
        messagebox.showinfo("功能开发中", "暂停比对功能正在开发中...")
        self._log_message("暂停比对功能调用", "INFO")

    def restart_comparison(self):
        """重新运行比对"""
        self.stop_comparison()
        self.clear_results()
        self.start_comparison()

    def clear_results(self):
        """清空结果"""
        # 重置进度条
        self.progress_bar['value'] = 0

        # 更新状态栏
        self.update_status_bar(status_text="结果已清空", progress_desc="", progress_value=0, speed=0, diff_count=0)

        # 如果当前显示的是结果页面，刷新显示
        if hasattr(self, 'current_page') and self.current_page:
            # 可以在这里刷新当前页面的结果显示
            pass

        self._log_message("清空比对结果", "INFO")

    def validate_config(self):
        """验证配置"""
        messagebox.showinfo("功能开发中", "配置验证功能正在开发中...")
        self._log_message("配置验证功能调用", "INFO")

    def test_connections(self):
        """测试连接"""
        messagebox.showinfo("功能开发中", "测试连接功能正在开发中...")
        self._log_message("测试连接功能调用", "INFO")

    # ==================== 工具菜单事件处理 ====================

    def manage_datasources(self):
        """数据源管理"""
        messagebox.showinfo("功能开发中", "数据源管理功能正在开发中...")
        self._log_message("数据源管理功能调用", "INFO")

    def sql_generator(self):
        """SQL生成器"""
        messagebox.showinfo("功能开发中", "SQL生成器功能正在开发中...")
        self._log_message("SQL生成器功能调用", "INFO")

    def export_results(self):
        """导出结果"""
        # 检查是否有比对结果数据
        if self.comparison_stats['total_differences'] == 0:
            messagebox.showwarning("警告", "没有可导出的结果数据")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出比对结果",
            defaultextension=".csv",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx"),
                ("JSON files", "*.json"),
                ("XML files", "*.xml")
            ]
        )
        if file_path:
            try:
                # 这里应该实现实际的导出逻辑
                self.update_status_bar(status_text=f"结果已导出: {Path(file_path).name}")
                self._log_message(f"导出结果到: {file_path}", "INFO")
                messagebox.showinfo("成功", f"结果已导出到: {Path(file_path).name}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")
                self._log_message(f"导出失败: {str(e)}", "ERROR")

    def show_performance_monitor(self):
        """显示性能监控"""
        # 切换到性能监控选项卡
        self.notebook.select(3)  # 性能监控是第4个选项卡
        self._log_message("切换到性能监控视图", "INFO")

    def show_log_viewer(self):
        """显示日志查看器"""
        # 切换到日志选项卡
        self.notebook.select(2)  # 日志是第3个选项卡
        self._log_message("切换到日志查看器", "INFO")

    def manage_plugins(self):
        """插件管理"""
        messagebox.showinfo("功能开发中", "插件管理功能正在开发中...")
        self._log_message("插件管理功能调用", "INFO")

    def show_system_info(self):
        """显示系统信息"""
        import platform
        import psutil

        info = f"""系统信息：
操作系统: {platform.system()} {platform.release()}
Python版本: {platform.python_version()}
CPU核心数: {psutil.cpu_count()}
内存总量: {psutil.virtual_memory().total // (1024**3)} GB
"""
        messagebox.showinfo("系统信息", info)
        self._log_message("显示系统信息", "INFO")

    # ==================== 视图菜单事件处理 ====================

    def refresh_interface(self):
        """刷新界面"""
        # 重新初始化导航树
        self._init_navigation_tree()

        # 更新时间显示
        self._update_time()

        self._update_status("界面已刷新")
        self._log_message("刷新界面", "INFO")

    def reset_layout(self):
        """重置布局"""
        # 重置分割面板比例
        if hasattr(self, 'main_horizontal_paned'):
            self.main_horizontal_paned.sash_place(0, 300, 0)
        if hasattr(self, 'main_vertical_paned'):
            # 设置底部信息区域为窗口高度的1/4
            window_height = self.root.winfo_height()
            self.main_vertical_paned.sash_place(0, 0, int(window_height * 0.75))

        self._update_status("布局已重置")
        self._log_message("重置界面布局", "INFO")

    def toggle_toolbar(self):
        """切换工具栏显示"""
        messagebox.showinfo("功能开发中", "工具栏切换功能正在开发中...")
        self._log_message("工具栏切换功能调用", "INFO")

    def toggle_statusbar(self):
        """切换状态栏显示"""
        messagebox.showinfo("功能开发中", "状态栏切换功能正在开发中...")
        self._log_message("状态栏切换功能调用", "INFO")

    def toggle_fullscreen(self):
        """切换全屏模式"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)

        mode = "全屏" if not current_state else "窗口"
        self._update_status(f"切换到{mode}模式")
        self._log_message(f"切换到{mode}模式", "INFO")

    def show_zoom_options(self):
        """显示缩放选项"""
        messagebox.showinfo("功能开发中", "缩放功能正在开发中...")
        self._log_message("缩放功能调用", "INFO")

    # ==================== 帮助菜单事件处理 ====================

    def show_user_manual(self):
        """显示用户手册"""
        messagebox.showinfo("功能开发中", "用户手册功能正在开发中...")
        self._log_message("用户手册功能调用", "INFO")

    def show_quick_start(self):
        """显示快速入门"""
        quick_start_text = """快速入门指南：

1. 新建或打开配置文件
2. 配置数据源连接信息
3. 选择要比对的表
4. 设置比对规则
5. 点击"开始比对"执行任务
6. 查看比对结果和日志

更多详细信息请参考用户手册。"""

        messagebox.showinfo("快速入门", quick_start_text)
        self._log_message("显示快速入门指南", "INFO")

    def show_shortcuts(self):
        """显示快捷键"""
        shortcuts_text = """快捷键列表：

文件操作：
Ctrl+N - 新建配置
Ctrl+O - 打开配置
Ctrl+S - 保存配置
Ctrl+Shift+S - 另存为

编辑操作：
Ctrl+Z - 撤销
Ctrl+Y - 重做
Ctrl+C - 复制
Ctrl+V - 粘贴
Ctrl+F - 查找
Ctrl+H - 替换
Ctrl+A - 全选

运行操作：
F9 - 开始比对
Shift+F9 - 停止比对
F8 - 暂停比对
Ctrl+F9 - 重新运行
Ctrl+L - 清空结果

视图操作：
F5 - 刷新界面
F11 - 全屏模式
Alt+F4 - 退出程序"""

        messagebox.showinfo("快捷键", shortcuts_text)
        self._log_message("显示快捷键列表", "INFO")

    def check_updates(self):
        """检查更新"""
        messagebox.showinfo("功能开发中", "检查更新功能正在开发中...")
        self._log_message("检查更新功能调用", "INFO")

    def report_issue(self):
        """反馈问题"""
        messagebox.showinfo("功能开发中", "问题反馈功能正在开发中...")
        self._log_message("问题反馈功能调用", "INFO")

    def show_about(self):
        """显示关于信息"""
        about_text = """SqlCompare 数据比对工具

版本: 1.0.0
作者: SqlCompare Team

这是一个高度可扩展、配置驱动的数据比对工具，
支持多种数据库类型，提供直观的图形用户界面。

特性：
• 支持多种数据库类型
• 配置驱动的比对规则
• 实时进度显示
• 详细的比对结果展示
• 完整的菜单系统
• 丰富的快捷键支持

技术栈：
• Python 3.x
• tkinter GUI框架
• 模块化架构设计

© 2024 SqlCompare Team. All rights reserved."""

        messagebox.showinfo("关于 SqlCompare", about_text)
        self._log_message("显示关于信息", "INFO")

    # ==================== 界面事件处理 ====================

    def on_tree_selection_changed(self, event):
        """处理树形控件选择变化事件 - 已废弃，使用 on_navigation_tree_selection"""
        # 这个方法保留是为了兼容性，但实际上不再使用
        pass

    def on_tab_changed(self, event):
        """处理选项卡切换事件 - 仅用于底部信息区域"""
        selected_tab = event.widget.tab('current')['text']
        self._log_message(f"切换到底部选项卡: {selected_tab}", "INFO")

    def on_result_double_click(self, event):
        """处理结果表格双击事件 - 已废弃，结果现在显示在页面组件中"""
        # 这个方法保留是为了兼容性，但实际上不再使用
        pass



    def _log_message(self, message, level="INFO"):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"

        # 检查日志文本框是否已创建且可用
        if hasattr(self, 'log_text') and self.log_text is not None:
            try:
                # 临时启用编辑模式
                self.log_text.configure(state=tk.NORMAL)

                # 插入日志条目
                self.log_text.insert(tk.END, log_entry)

                # 自动滚动到底部
                self.log_text.see(tk.END)

                # 重新设置为只读模式
                self.log_text.configure(state=tk.DISABLED)
            except Exception as e:
                # 如果插入失败，回退到控制台输出
                print(f"[{level}] {message} (日志写入失败: {e})")
        else:
            # 日志文本框未创建时，输出到控制台
            print(f"[{level}] {message}")

    # ==================== 工具方法 ====================

    def _update_status(self, message):
        """更新状态栏信息 - 兼容性方法"""
        self.update_status_bar(status_text=message)

    def _update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if hasattr(self, 'time_label'):
            self.time_label.config(text=current_time)

        # 每秒更新一次
        self.root.after(1000, self._update_time)













    def run(self):
        """运行应用程序"""
        self.root.mainloop()

# ==================== 应用程序入口 ====================

def main():
    """主函数"""
    try:
        app = SqlCompareApp()
        app.run()
    except Exception as e:
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
