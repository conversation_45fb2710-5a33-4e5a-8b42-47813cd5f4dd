# core/models.py
import json
from dataclasses import dataclass
from typing import Any, Optional, List, Iterator


class Record:
    """代表一条数据记录"""
    __slots__ = ('key', 'value')

    def __init__(self, key: str, value: Any):
        self.key = key
        self.value = value

    def __repr__(self):
        return f"Record(key='{self.key}', value={self.value})"

    def __eq__(self, other):
        if not isinstance(other, Record):
            return False
        return self.key == other.key and self.value == other.value

    def __hash__(self):
        try:
            return hash((self.key, str(self.value)))
        except TypeError:
            return hash(self.key)

def create_record_batch(batch_rows: List[tuple], value_columns: tuple) -> Iterator[Record]:
    """批量创建Record对象"""
    value_columns_len = len(value_columns)

    # 预缓存常用对象和函数，减少查找开销
    str_type = str
    isinstance_func = isinstance
    Record_class = Record

    if value_columns_len == 0:
        # 只有key列的情况 - 最优化路径
        for row_tuple in batch_rows:
            key = row_tuple[0] if isinstance_func(row_tuple[0], str_type) else str_type(row_tuple[0])
            yield Record_class(key, {})

    elif value_columns_len == 1:
        # 单列优化 - 避免循环和zip
        col_name = value_columns[0]
        for row_tuple in batch_rows:
            key = row_tuple[0] if isinstance_func(row_tuple[0], str_type) else str_type(row_tuple[0])
            value_dict = {col_name: row_tuple[1]}
            yield Record_class(key, value_dict)

    elif value_columns_len <= 5:
        # 少量列直接构建 - 避免zip开销
        for row_tuple in batch_rows:
            key = row_tuple[0] if isinstance_func(row_tuple[0], str_type) else str_type(row_tuple[0])
            value_dict = {value_columns[i]: row_tuple[i + 1] for i in range(value_columns_len)}
            yield Record_class(key, value_dict)

    else:
        # 多列情况 - 使用优化的字典构建
        for row_tuple in batch_rows:
            key = row_tuple[0] if isinstance_func(row_tuple[0], str_type) else str_type(row_tuple[0])
            # 使用dict()构造器比字典推导式稍快
            value_dict = dict(zip(value_columns, row_tuple[1:]))
            yield Record_class(key, value_dict)

def create_single_record(row_tuple: tuple, value_columns: tuple) -> Record:
    """创建单个Record对象"""
    value_columns_len = len(value_columns)

    # 优化key处理
    key = row_tuple[0] if isinstance(row_tuple[0], str) else str(row_tuple[0])

    # 优化值字典构建
    if value_columns_len == 0:
        return Record(key, {})
    elif value_columns_len == 1:
        value_dict = {value_columns[0]: row_tuple[1]}
        return Record(key, value_dict)
    else:
        value_dict = {value_columns[i]: row_tuple[i + 1] for i in range(value_columns_len)}
        return Record(key, value_dict)

def compare_values_optimized(value_a: Any, value_b: Any) -> bool:
    """优化的值比较函数 - 纯Python版本"""
    # 身份比较 - 最快的比较方式
    if value_a is value_b:
        return True

    # None检查 - 快速路径
    if value_a is None or value_b is None:
        return False

    # 类型检查优化
    if type(value_a) is type(value_b):
        return value_a == value_b

    # 不同类型时的字符串比较
    str_func = str
    return str_func(value_a) == str_func(value_b)

def process_db_batch_python(batch_rows: List[tuple], columns: tuple) -> List[Record]:
    """批量数据处理函数"""
    batch_size = len(batch_rows)
    value_columns = columns[1:] if len(columns) > 1 else ()

    # 预分配结果列表
    results = [None] * batch_size

    # 使用批量创建函数
    for i, record in enumerate(create_record_batch(batch_rows, value_columns)):
        results[i] = record

    return results

@dataclass
class DiffResult:
    """代表一条比对差异的结果。"""
    key: str
    status: str
    value_a: Optional[Any] = None
    value_b: Optional[Any] = None

    def __post_init__(self):
        if hasattr(self.status, 'value'):
            self.status = self.status.value

def create_comparison_record(
    task_id: str,
    table_name: str,
    record_key: Any,
    status: Any,
    field_name: Optional[str] = None,
    source_value: Optional[Any] = None,
    target_value: Optional[Any] = None
) -> dict:
    """
    直接创建符合ComparisonResult数据库模型要求的字典格式

    Args:
        task_id: 任务ID
        table_name: 表名
        record_key: 记录键
        status: 差异状态（DiffStatus枚举或字符串）
        field_name: 字段名（可选）
        source_value: 源值（可选）
        target_value: 目标值（可选）

    Returns:
        符合ComparisonResult模型的字典
    """
    # 处理status字段 - 确保使用枚举值
    if hasattr(status, 'value'):
        status_value = status.value
    elif isinstance(status, str) and status.startswith('DiffStatus.'):
        # 处理字符串形式的枚举名称
        from models.sqlalchemy_models import DiffStatus
        enum_name = status.replace('DiffStatus.', '')
        status_mapping = {
            'IDENTICAL': DiffStatus.IDENTICAL.value,      # 'ID'
            'DIFFERENT': DiffStatus.DIFFERENT.value,      # 'DF'
            'SOURCE_ONLY': DiffStatus.SOURCE_ONLY.value,  # 'SO'
            'TARGET_ONLY': DiffStatus.TARGET_ONLY.value,  # 'TO'
            'FIELD_DIFF': DiffStatus.FIELD_DIFF.value     # 'FD'
        }
        status_value = status_mapping.get(enum_name, status[:2])
    else:
        status_value = str(status)[:2] if status else ''

    # 处理record_key字段 - 确保是字符串
    if isinstance(record_key, dict):
        try:
            key_str = json.dumps(record_key, ensure_ascii=False)
        except (TypeError, ValueError):
            key_str = str(record_key)
    elif record_key is not None:
        key_str = str(record_key)
    else:
        key_str = ''

    # 处理value字段 - 将字典转换为JSON字符串
    def process_value(value):
        if value is None:
            return None
        elif isinstance(value, dict):
            try:
                return json.dumps(value, ensure_ascii=False)
            except (TypeError, ValueError):
                return str(value)
        else:
            return str(value)

    return {
        'task_id': str(task_id),
        'table_name': str(table_name),
        'record_key': key_str,
        'status': status_value,
        'field_name': str(field_name) if field_name is not None else None,
        'source_value': process_value(source_value),
        'target_value': process_value(target_value)
    }