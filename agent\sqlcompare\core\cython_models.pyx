# cython: language_level=3
# cython: boundscheck=False
# cython: wraparound=False
# cython: cdivision=True
# cython: profile=False

"""
Cython优化的数据模型
专门用于高性能数据读取和Record对象创建
"""

import cython
from typing import Any, Dict, List, Tuple
from libc.stdlib cimport malloc, free
from libc.string cimport strcpy, strlen
from cpython.object cimport PyObject
from cpython.dict cimport PyDict_New, PyDict_SetItem
from cpython.unicode cimport PyUnicode_FromString, PyUnicode_AsUTF8

cdef class CythonRecord:
    """Cython优化的Record类 - 极致性能版本"""
    
    cdef public str key
    cdef public object value
    
    def __init__(self, str key, object value):
        self.key = key
        self.value = value
    
    def __repr__(self):
        return f"CythonRecord(key='{self.key}', value={self.value})"
    
    def __eq__(self, other):
        if not isinstance(other, (<PERSON><PERSON>onR<PERSON>ord, Record)):
            return False
        return self.key == other.key and self.value == other.value

# 高性能批量Record创建函数
@cython.boundscheck(False)
@cython.wraparound(False)
def create_record_batch(list batch_rows, tuple value_columns):
    """
    批量创建Record对象 - Cython优化版本
    
    Args:
        batch_rows: 数据库返回的行数据列表
        value_columns: 值列名元组
        
    Returns:
        生成器，产出CythonRecord对象
    """
    cdef int i, j
    cdef int batch_size = len(batch_rows)
    cdef int value_columns_len = len(value_columns)
    cdef tuple row_tuple
    cdef str key
    cdef dict value_dict
    cdef object row_value
    
    # 预分配和缓存常用对象
    str_type = str
    isinstance_func = isinstance
    
    for i in range(batch_size):
        row_tuple = batch_rows[i]
        
        # 优化key处理 - 内联类型检查
        if isinstance_func(row_tuple[0], str_type):
            key = row_tuple[0]
        else:
            key = str_type(row_tuple[0])
        
        # 优化值字典构建
        if value_columns_len == 0:
            # 只有key列的情况
            yield CythonRecord(key, {})
        elif value_columns_len == 1:
            # 单列优化 - 直接构建
            value_dict = {value_columns[0]: row_tuple[1]}
            yield CythonRecord(key, value_dict)
        else:
            # 多列情况 - 优化字典构建
            value_dict = {}
            for j in range(value_columns_len):
                value_dict[value_columns[j]] = row_tuple[j + 1]
            yield CythonRecord(key, value_dict)

@cython.boundscheck(False)
@cython.wraparound(False)
def create_single_record(tuple row_tuple, tuple value_columns):
    """
    创建单个Record对象 - 内联优化版本
    
    Args:
        row_tuple: 单行数据元组
        value_columns: 值列名元组
        
    Returns:
        CythonRecord对象
    """
    cdef int value_columns_len = len(value_columns)
    cdef str key
    cdef dict value_dict
    cdef int j
    
    # 优化key处理
    if isinstance(row_tuple[0], str):
        key = row_tuple[0]
    else:
        key = str(row_tuple[0])
    
    # 优化值字典构建
    if value_columns_len == 0:
        return CythonRecord(key, {})
    elif value_columns_len == 1:
        value_dict = {value_columns[0]: row_tuple[1]}
        return CythonRecord(key, value_dict)
    else:
        value_dict = {}
        for j in range(value_columns_len):
            value_dict[value_columns[j]] = row_tuple[j + 1]
        return CythonRecord(key, value_dict)

# 高性能字符串比较函数 - 配合engine.py使用
@cython.boundscheck(False)
@cython.wraparound(False)
cdef inline bint compare_values_cython(object value_a, object value_b):
    """Cython内联值比较函数"""
    # 身份比较 - 最快
    if value_a is value_b:
        return True
    
    # None检查
    if value_a is None or value_b is None:
        return False
    
    # 类型比较优化
    if type(value_a) is type(value_b):
        return value_a == value_b
    
    # 不同类型的字符串比较
    return str(value_a) == str(value_b)

def compare_values_cython_wrapper(object value_a, object value_b):
    """Cython比较函数的Python包装器"""
    return compare_values_cython(value_a, value_b)

# 批量数据处理优化函数
@cython.boundscheck(False)
@cython.wraparound(False)
def process_db_batch_optimized(list batch_rows, tuple columns, int batch_size):
    """
    优化的批量数据处理函数
    
    Args:
        batch_rows: 数据库批量行数据
        columns: 列名元组
        batch_size: 批次大小
        
    Returns:
        处理后的Record对象列表
    """
    cdef int i
    cdef list results = []
    cdef tuple row_tuple
    cdef str key
    cdef dict value_dict
    cdef int columns_len = len(columns)
    cdef tuple value_columns = columns[1:] if columns_len > 1 else ()
    cdef int value_columns_len = len(value_columns)
    
    # 预分配结果列表容量
    results = [None] * batch_size
    
    for i in range(batch_size):
        row_tuple = batch_rows[i]
        
        # 内联key处理
        if isinstance(row_tuple[0], str):
            key = row_tuple[0]
        else:
            key = str(row_tuple[0])
        
        # 内联值处理
        if value_columns_len == 0:
            results[i] = CythonRecord(key, {})
        elif value_columns_len == 1:
            value_dict = {value_columns[0]: row_tuple[1]}
            results[i] = CythonRecord(key, value_dict)
        else:
            value_dict = {value_columns[j]: row_tuple[j + 1] for j in range(value_columns_len)}
            results[i] = CythonRecord(key, value_dict)
    
    return results

# 兼容性导入
try:
    # 尝试相对导入
    from .models import Record
except ImportError:
    try:
        # 尝试绝对导入
        from models import Record
    except ImportError:
        # 如果无法导入，定义一个简单的Record类
        class Record:
            def __init__(self, key, value):
                self.key = key
                self.value = value
