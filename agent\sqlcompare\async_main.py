import os
import sys
import time
import asyncio
import logging
from typing import Optional, Dict, Any
from utils.config_manager import SmartConfigManager
from connectors.asynchronous.db2_connector import AsyncDB2Connector
from reporters.asynchronous.csv_reporter import AsyncCsvReporter
from core.asynchronous.engine import AsyncComparisonEngine, ComparisonConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AsyncComparisonOrchestrator:
    """异步比对协调器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化异步比对协调器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_manager = SmartConfigManager()
        self.config_path = config_path or self._get_default_config_path()
        self.comparison_engine = None
        
        # 性能指标
        self.performance_metrics = {
            'total_time': 0.0,
            'config_load_time': 0.0,
            'connection_time': 0.0,
            'comparison_time': 0.0,
            'report_time': 0.0
        }
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        config_files = self.config_manager.auto_discover_configs()
        if config_files:
            return config_files[0]
        return os.path.join(os.path.dirname(__file__), 'config', 'Config.ini')
    
    async def run_comparison(self, progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        运行异步比对
        
        Args:
            progress_callback: 进度回调函数
            
        Returns:
            比对结果和性能指标
        """
        start_time = time.time()
        
        try:
            logger.info("开始异步并发数据比对...")
            
            # 1. 加载配置
            config_start = time.time()
            if not await self._load_config():
                raise RuntimeError("配置加载失败")
            self.performance_metrics['config_load_time'] = time.time() - config_start
            
            # 2. 创建连接器和报告器
            connection_start = time.time()
            source_a, source_b, reporter = await self._create_components()
            self.performance_metrics['connection_time'] = time.time() - connection_start
            
            # 3. 配置比对引擎
            comparison_config = self._create_comparison_config()
            self.comparison_engine = AsyncComparisonEngine(comparison_config)
            
            # 4. 根据配置选择比对方法并执行
            comparison_start = time.time()

            if self.cmp_type == 1:
                # 使用异步流式归并算法
                logger.info("开始执行异步流式归并比对...")
                comparison_result = await self.comparison_engine.compare_sources_stream(
                    source_a, source_b, reporter, progress_callback
                )
            elif self.cmp_type == 2:
                # 使用异步内存字典算法
                logger.info("开始执行异步内存字典比对...")
                comparison_result = await self.comparison_engine.compare_sources_memory(
                    source_a, source_b, reporter, progress_callback
                )
            else:
                raise ValueError(f"不支持的比对类型: {self.cmp_type}，支持的类型: 1(流式归并), 2(内存字典)")

            self.performance_metrics['comparison_time'] = time.time() - comparison_start
            
            # 5. 计算总时间
            self.performance_metrics['total_time'] = time.time() - start_time
            
            # 6. 生成结果报告
            result = {
                'success': True,
                'comparison_metrics': comparison_result,
                'performance_metrics': self.performance_metrics,
                'config_info': await self._get_config_info()
            }
            
            logger.info(f"异步并发比对完成，总耗时: {self.performance_metrics['total_time']:.2f}秒")
            return result
            
        except Exception as e:
            logger.error(f"异步比对过程中发生错误: {e}")
            self.performance_metrics['total_time'] = time.time() - start_time
            return {
                'success': False,
                'error': str(e),
                'performance_metrics': self.performance_metrics
            }
    
    async def _load_config(self) -> bool:
        """异步加载配置"""
        try:
            # 在线程池中执行同步的配置加载
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                None, self.config_manager.load_config, self.config_path
            )
            
            if success:
                logger.info(f"配置加载成功: {self.config_path}")
                return True
            else:
                logger.error("配置加载失败")
                return False
                
        except Exception as e:
            logger.error(f"配置加载异常: {e}")
            return False
    
    async def _create_components(self):
        """创建连接器和报告器组件"""
        try:
            # 提取数据库配置
            db1_config = dict(self.config_manager.config['DB1'])
            db2_config = dict(self.config_manager.config['DB2'])

            # 获取比对类型配置
            common_config = dict(self.config_manager.config['COMMON'])
            self.cmp_type = int(common_config.get('CMP_TYPE', 1))  # 默认为1

            logger.info(f"使用比对模式: {self.cmp_type} ({'流式归并算法' if self.cmp_type == 1 else '内存字典算法'})")

            # 提取SQL规则
            table_rule = self.config_manager.rules.find('table')
            if table_rule is None:
                raise ValueError("在规则文件中找不到任何 'table' 规则")

            sql1 = table_rule.find('sql_1').text.strip()
            sql2 = table_rule.find('sql_2').text.strip()
            self.table_name = table_rule.get('table_id', 'unknown')

            # 创建异步连接器
            source_a = AsyncDB2Connector(db1_config, query=sql1, chunk_size=1000, max_workers=2)
            source_b = AsyncDB2Connector(db2_config, query=sql2, chunk_size=1000, max_workers=2)

            # 创建异步报告器
            reporter_config = {
                'filepath': 'async_comparison_report.csv',
                'encoding': 'utf-8-sig',
                'include_header': True,
                'include_timestamp': True
            }
            reporter = AsyncCsvReporter(config=reporter_config, batch_size=100)

            return source_a, source_b, reporter

        except (KeyError, AttributeError) as e:
            logger.error(f"配置文件格式错误: {e}")
            raise
        except ValueError as e:
            logger.error(f"CMP_TYPE配置值无效，必须为数字: {e}")
            raise
        except Exception as e:
            logger.error(f"组件创建失败: {e}")
            raise
    
    def _create_comparison_config(self) -> ComparisonConfig:
        """创建比对配置"""
        return ComparisonConfig(
            buffer_size=1000,
            max_concurrent_reads=2,
            read_timeout=30.0,
            enable_progress_callback=True,
            memory_limit_mb=100
        )
    
    async def _get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            'config_path': self.config_path,
            'db1_host': self.config_manager.config['DB1']['ip'],
            'db2_host': self.config_manager.config['DB2']['ip'],
            'rules_count': len(self.config_manager.rules.findall('table'))
        }


async def progress_callback(metrics: Dict[str, Any]):
    """进度回调函数"""
    total_records = metrics.get('total_records_a', 0) + metrics.get('total_records_b', 0)
    differences = metrics.get('differences', 0)
    logger.info(f"处理进度: 已处理 {total_records} 条记录, "
                f"发现 {differences} 个差异")


async def main():
    """异步主函数"""
    try:
        # 创建比对协调器
        orchestrator = AsyncComparisonOrchestrator()
        
        # 运行异步比对
        result = await orchestrator.run_comparison(progress_callback)
        
        if result['success']:
            print("\n=== 异步并发比对完成 ===")
            print(f"总耗时: {result['performance_metrics']['total_time']:.2f}秒")
            print(f"配置加载: {result['performance_metrics']['config_load_time']:.2f}秒")
            print(f"连接建立: {result['performance_metrics']['connection_time']:.2f}秒")
            print(f"数据比对: {result['performance_metrics']['comparison_time']:.2f}秒")
            
            comparison_metrics = result['comparison_metrics']
            print(f"\n=== 比对结果统计 ===")
            total_records = comparison_metrics['total_records_a'] + comparison_metrics['total_records_b']
            print(f"处理记录数: {total_records:,}")
            print(f"源端A记录: {comparison_metrics['total_records_a']:,}")
            print(f"源端B记录: {comparison_metrics['total_records_b']:,}")
            print(f"发现差异: {comparison_metrics['differences']:,}")
            print(f"处理速率: {comparison_metrics['processing_rate']:.2f} 条/秒")
            print(f"并发效率: {comparison_metrics['concurrent_efficiency']:.2f}")
            
            print(f"\n报告已生成: async_comparison_report.csv")
        else:
            print(f"异步比对失败: {result['error']}")
            
    except KeyboardInterrupt:
        print("\n用户中断比对过程")
    except Exception as e:
        print(f"程序执行失败: {e}")
        logger.exception("程序执行异常")

if __name__ == '__main__':    
    asyncio.run(main())
