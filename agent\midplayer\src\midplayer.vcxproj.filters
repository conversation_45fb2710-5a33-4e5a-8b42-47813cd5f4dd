﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="utils">
      <UniqueIdentifier>{b1555c79-6ce8-4fd0-804d-ccbaad6dfd85}</UniqueIdentifier>
    </Filter>
    <Filter Include="mid">
      <UniqueIdentifier>{ec01a6dc-0bb4-485c-a187-ab0d92babe7c}</UniqueIdentifier>
    </Filter>
    <Filter Include="log">
      <UniqueIdentifier>{ec98730e-4509-437f-b713-b6a28ba7ff79}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="midplayer.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="utils\BlowFish.cpp">
      <Filter>utils</Filter>
    </ClCompile>
    <ClCompile Include="utils\crc.cpp">
      <Filter>utils</Filter>
    </ClCompile>
    <ClCompile Include="mid\AtsMidUtility.cpp">
      <Filter>mid</Filter>
    </ClCompile>
    <ClCompile Include="mid\MidGateway.cpp">
      <Filter>mid</Filter>
    </ClCompile>
    <ClCompile Include="mid\MiniSocket.cpp">
      <Filter>mid</Filter>
    </ClCompile>
    <ClCompile Include="spdlog\src\async.cpp">
      <Filter>log</Filter>
    </ClCompile>
    <ClCompile Include="spdlog\src\bundled_fmtlib_format.cpp">
      <Filter>log</Filter>
    </ClCompile>
    <ClCompile Include="spdlog\src\cfg.cpp">
      <Filter>log</Filter>
    </ClCompile>
    <ClCompile Include="spdlog\src\color_sinks.cpp">
      <Filter>log</Filter>
    </ClCompile>
    <ClCompile Include="spdlog\src\file_sinks.cpp">
      <Filter>log</Filter>
    </ClCompile>
    <ClCompile Include="spdlog\src\spdlog.cpp">
      <Filter>log</Filter>
    </ClCompile>
    <ClCompile Include="spdlog\src\stdout_sinks.cpp">
      <Filter>log</Filter>
    </ClCompile>
    <ClCompile Include="utils\AXCommon.cpp">
      <Filter>utils</Filter>
    </ClCompile>
    <ClCompile Include="utils\error.cpp">
      <Filter>utils</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="utils\base.h">
      <Filter>utils</Filter>
    </ClInclude>
    <ClInclude Include="utils\Blowfish.h">
      <Filter>utils</Filter>
    </ClInclude>
    <ClInclude Include="utils\crc.h">
      <Filter>utils</Filter>
    </ClInclude>
    <ClInclude Include="mid\AtsMidUtility.h">
      <Filter>mid</Filter>
    </ClInclude>
    <ClInclude Include="mid\MidGateway.h">
      <Filter>mid</Filter>
    </ClInclude>
    <ClInclude Include="mid\MiniSocket.h">
      <Filter>mid</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utils\AXCommon.h">
      <Filter>utils</Filter>
    </ClInclude>
    <ClInclude Include="utils\platform.h">
      <Filter>utils</Filter>
    </ClInclude>
    <ClInclude Include="utils\error.h">
      <Filter>utils</Filter>
    </ClInclude>
  </ItemGroup>
</Project>