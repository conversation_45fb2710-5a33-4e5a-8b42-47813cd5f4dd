#!/usr/bin/env python3
"""
SQLCompare Cython扩展构建脚本
用于编译所有Cython优化模块
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查构建依赖"""
    try:
        import Cython
        print(f"✓ Cython版本: {Cython.__version__}")
    except ImportError:
        print("✗ 缺少Cython，请安装: pip install Cython")
        return False
    
    try:
        import numpy
        print(f"✓ NumPy版本: {numpy.__version__}")
    except ImportError:
        print("✗ 缺少NumPy，请安装: pip install numpy")
        return False
    
    return True

def clean_build_files():
    """清理之前的构建文件和中间文件"""
    print("清理Cython构建产生的中间文件...")

    current_dir = Path(__file__).parent

    # 定义需要清理的文件模式
    file_patterns = [
        "**/*.c",           # Cython生成的C文件
        "**/*.cpp",         # Cython生成的C++文件
        "**/*.html",        # Cython注释文件
        "**/*.pyc",         # Python字节码文件
        "**/*.pyo",         # Python优化字节码文件
    ]

    # 定义需要清理的目录模式
    dir_patterns = [
        "**/build/",        # 构建目录
        "**/__pycache__/",  # Python缓存目录
        "**/dist/",         # 分发目录
        "**/*.egg-info/",   # 包信息目录
    ]

    cleaned_files = 0
    cleaned_dirs = 0

    # 清理文件
    for pattern in file_patterns:
        for file_path in current_dir.glob(pattern):
            if file_path.is_file():
                try:
                    # 对于.pyd文件，尝试强制删除
                    if file_path.suffix == '.pyd':
                        import time
                        # 等待一下，让可能的进程释放文件
                        time.sleep(0.1)
                        # 尝试修改文件权限
                        try:
                            import stat
                            file_path.chmod(stat.S_IWRITE)
                        except:
                            pass

                    file_path.unlink()
                    print(f"删除文件: {file_path}")
                    cleaned_files += 1
                except OSError as e:
                    if file_path.suffix == '.pyd':
                        print(f"⚠️ .pyd文件正在使用中，无法删除: {file_path.name}")
                        print("   提示: 请关闭所有Python进程后重试，或重启后再运行清理")
                    else:
                        print(f"⚠️ 无法删除文件 {file_path}: {e}")

    # 清理目录
    for pattern in dir_patterns:
        for dir_path in current_dir.glob(pattern):
            if dir_path.is_dir():
                try:
                    shutil.rmtree(dir_path)
                    print(f"删除目录: {dir_path}")
                    cleaned_dirs += 1
                except OSError as e:
                    print(f"⚠️ 无法删除目录 {dir_path}: {e}")

    print(f"清理完成: {cleaned_files}个文件, {cleaned_dirs}个目录")

def build_cython_models():
    """构建Cython模型扩展"""
    print("\n=== 构建Cython模型扩展 ===")

    # 保存当前目录
    original_dir = os.getcwd()

    # 确定core目录的正确路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    core_dir = os.path.join(script_dir, "core")

    print(f"脚本目录: {script_dir}")
    print(f"Core目录: {core_dir}")

    # 检查core目录是否存在
    if not os.path.exists(core_dir):
        print(f"✗ 找不到core目录: {core_dir}")
        return False
    else:
        print(f"✓ 找到core目录")

    try:
        os.chdir(core_dir)

        # 检查必要文件
        pyx_file = "cython_models.pyx"
        setup_file = "setup_cython_models.py"

        if not os.path.exists(pyx_file):
            print(f"✗ 找不到源文件: {pyx_file}")
            return False

        if not os.path.exists(setup_file):
            print(f"✗ 找不到构建脚本: {setup_file}")
            return False

        print(f"当前工作目录: {os.getcwd()}")
        print(f"源文件: {pyx_file} - {'存在' if os.path.exists(pyx_file) else '不存在'}")

        result = subprocess.run([
            sys.executable, setup_file,
            "build_ext", "--inplace"
        ], capture_output=True, text=True, cwd=core_dir)

        if result.returncode == 0:
            print("✓ Cython模型扩展构建成功")
            if result.stdout:
                print("构建输出:", result.stdout[-500:])  # 显示最后500字符
            return True
        else:
            print(f"✗ Cython模型扩展构建失败:")
            print("STDERR:", result.stderr)
            if result.stdout:
                print("STDOUT:", result.stdout)
            return False
    except Exception as e:
        print(f"✗ 构建过程异常: {e}")
        return False
    finally:
        os.chdir(original_dir)

def build_cython_engine():
    """构建Cython引擎扩展"""
    print("\n=== 构建Cython引擎扩展 ===")

    # 保存当前目录
    original_dir = os.getcwd()

    # 确定core目录的正确路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    core_dir = os.path.join(script_dir, "core")

    # 检查引擎源文件
    engine_pyx = os.path.join(core_dir, "cython_engine.pyx")
    print(f"检查引擎源文件: {engine_pyx}")

    if not os.path.exists(engine_pyx):
        print(f"✗ 找不到引擎源文件: {engine_pyx}")
        return False
    else:
        print(f"✓ 找到引擎源文件")

    # 创建引擎构建脚本
    engine_setup = f"""
import os
from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy as np

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
pyx_file = os.path.join(current_dir, "cython_engine.pyx")

# 检查.pyx文件是否存在
if not os.path.exists(pyx_file):
    raise FileNotFoundError(f"找不到Cython源文件: {{pyx_file}}")

extensions = [
    Extension(
        "cython_engine",  # 简化模块名
        [pyx_file],  # 使用绝对路径
        include_dirs=[np.get_include()],
        extra_compile_args=[
            "-O3",
            "-DNPY_NO_DEPRECATED_API=NPY_1_7_API_VERSION"
        ] + (["-march=native", "-ffast-math", "-funroll-loops"] if os.name != 'nt' else []),
        extra_link_args=["-O3"] if os.name != 'nt' else [],
        language="c"
    )
]

compiler_directives = {{
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'cdivision': True,
    'profile': False,
    'linetrace': False,
    'binding': False,
    'embedsignature': True,
    'optimize.use_switch': True,
    'optimize.unpack_method_calls': True
}}

setup(
    name="sqlcompare-cython-engine",
    ext_modules=cythonize(extensions, compiler_directives=compiler_directives, annotate=True),
    zip_safe=False,
    python_requires=">=3.8"
)
"""

    setup_file = os.path.join(core_dir, "setup_cython_engine.py")

    try:
        with open(setup_file, "w", encoding="utf-8") as f:
            f.write(engine_setup)

        os.chdir(core_dir)

        result = subprocess.run([
            sys.executable, "setup_cython_engine.py",
            "build_ext", "--inplace"
        ], capture_output=True, text=True, cwd=core_dir)

        if result.returncode == 0:
            print("✓ Cython引擎扩展构建成功")
            if result.stdout:
                print("构建输出:", result.stdout[-500:])
            return True
        else:
            print(f"✗ Cython引擎扩展构建失败:")
            print("STDERR:", result.stderr)
            if result.stdout:
                print("STDOUT:", result.stdout)
            return False
    except Exception as e:
        print(f"✗ 引擎构建过程异常: {e}")
        return False
    finally:
        os.chdir(original_dir)
        # 清理临时文件
        if os.path.exists(setup_file):
            try:
                os.remove(setup_file)
            except:
                pass

def test_cython_imports():
    """测试Cython模块导入"""
    print("\n=== 测试Cython模块导入 ===")

    # 确定正确的core目录路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    core_dir = os.path.join(script_dir, "core")

    print(f"添加路径到Python: {core_dir}")

    # 添加core目录到Python路径
    if core_dir not in sys.path:
        sys.path.insert(0, core_dir)

    # 验证.pyd文件存在
    pyd_files = []
    for file in os.listdir(core_dir):
        if file.endswith('.pyd') and ('cython_models' in file or 'cython_engine' in file):
            pyd_files.append(file)
            print(f"找到扩展文件: {file}")

    if not pyd_files:
        print("✗ 未找到.pyd扩展文件")
        return False

    success = True

    try:
        # 测试模型导入
        import cython_models
        from cython_models import CythonRecord, create_record_batch
        print("✓ Cython模型导入成功")

        # 简单测试
        record = CythonRecord("test_key", {"field": "value"})
        print(f"✓ CythonRecord创建测试: {record}")

        # 测试批量创建
        test_data = [("key1", "val1", 123), ("key2", "val2", 456)]
        test_columns = ("field1", "field2")
        records = list(create_record_batch(test_data, test_columns))
        print(f"✓ 批量Record创建测试: {len(records)} 条记录")

    except ImportError as e:
        print(f"✗ Cython模型导入失败: {e}")
        success = False
    except Exception as e:
        print(f"✗ Cython模型测试失败: {e}")
        success = False

    try:
        # 测试引擎导入
        import cython_engine
        from cython_engine import get_cython_engine_info
        info = get_cython_engine_info()
        print(f"✓ Cython引擎导入成功: {info}")

    except ImportError as e:
        print(f"✗ Cython引擎导入失败: {e}")
        success = False
    except Exception as e:
        print(f"✗ Cython引擎测试失败: {e}")
        success = False

    return success

def main():
    """主构建流程"""
    import argparse

    parser = argparse.ArgumentParser(description='SQLCompare Cython扩展构建器')
    parser.add_argument('--clean', action='store_true',
                       help='只清理构建文件，不进行构建')
    parser.add_argument('--no-clean', action='store_true',
                       help='跳过清理步骤，直接构建')

    args = parser.parse_args()

    print("SQLCompare Cython扩展构建器")
    print("=" * 50)

    # 如果只是清理
    if args.clean:
        print("\n=== 清理构建文件 ===")
        clean_build_files()
        print("\n🧹 清理完成！")
        return

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    # 清理旧文件（除非指定跳过）
    if not args.no_clean:
        print("\n=== 清理构建文件 ===")
        clean_build_files()

    # 构建扩展
    success = True
    success &= build_cython_models()
    success &= build_cython_engine()

    if success:
        # 测试导入
        success &= test_cython_imports()

    clean_build_files()

    if success:
        print("\n" + "=" * 50)
        print("🎉 所有Cython扩展构建成功！")
        print("\n性能提升预期:")
        print("- Record对象创建: 2-3倍提升")
        print("- 数据读取循环: 3-5倍提升")
        print("- 值比较函数: 2-4倍提升")
        print("- 整体端到端: 1.5-2.5倍提升")
        print("\n使用方法:")
        print("- DB2Connector(..., use_cython=True)")
        print("- compare_sources_hybrid(..., use_cython=True)")
    else:
        print("\n" + "=" * 50)
        print("❌ 构建过程中出现错误")
        print("请检查错误信息并重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
