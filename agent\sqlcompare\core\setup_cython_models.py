"""
Cython模型编译脚本
用于编译cython_models.pyx为高性能的C扩展
"""

from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy as np

# Cython扩展配置
import os

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
pyx_file = os.path.join(current_dir, "cython_models.pyx")

# 检查.pyx文件是否存在
if not os.path.exists(pyx_file):
    raise FileNotFoundError(f"找不到Cython源文件: {pyx_file}")

extensions = [
    Extension(
        "cython_models",  # 简化模块名
        [pyx_file],  # 使用绝对路径
        include_dirs=[np.get_include()],
        extra_compile_args=[
            "-O3",  # 最高优化级别
            "-DNPY_NO_DEPRECATED_API=NPY_1_7_API_VERSION"
        ] + (["-march=native", "-ffast-math", "-funroll-loops"] if os.name != 'nt' else []),  # Windows兼容性
        extra_link_args=["-O3"] if os.name != 'nt' else [],  # Windows兼容性
        language="c"
    )
]

# 编译配置
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'cdivision': True,
    'profile': False,
    'linetrace': False,
    'binding': False,
    'embedsignature': True,
    'optimize.use_switch': True,
    'optimize.unpack_method_calls': True
}

if __name__ == "__main__":
    setup(
        name="sqlcompare-cython-models",
        ext_modules=cythonize(
            extensions,
            compiler_directives=compiler_directives,
            annotate=True  # 生成HTML注释文件用于性能分析
        ),
        zip_safe=False,
        python_requires=">=3.8"
    )
