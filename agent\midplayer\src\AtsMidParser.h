#ifndef _AXDOC_PARSER_H__
#define _AXDOC_PARSER_H__
#pragma once
#include <iostream>
#include <iomanip>
#include <sstream>
#include <thread>
#include <mutex>
#include <string_view>
#include <condition_variable>

class AtsMidParser
{
public:
	AtsMidParser() {}
	~AtsMidParser() {}

public:
	bool win_readNetis_Bymmap(const std::string& filename);
	bool linux_readNetis_Bymmap(const std::string& filename);

	static bool isValidMidRequest(uint64_t line, std::string& row);
	static bool extractReqRes(uint64_t line, const std::string& row, std::string& req, std::string& res);
	static bool extractReqResFromLine(uint64_t line, const std::string& row, std::string& req, std::string& res, std::string& stk, std::string& cls);
	static std::string extractFields(uint64_t line, const std::string& row, int field);
	static bool extractFields(uint64_t line, const std::string& row, int field, std::string& val);
	static bool extractFields(const char* data, int codeField, int detailField, std::string& codeValue, std::string& detailValue);

private:
	std::string getSystemErrorMessage();
	std::string convertToTimestamp(double time_tick);

	bool handleCompleteMidLine(const std::string& line, uint64_t pos, uint64_t num);

	void handleOneIllegalMidLine(const std::string& line, int64_t pos, uint64_t num);

	void printWorkerProgress(uint64_t& numLine, uint64_t dealLine);
	void printReplayProgress(uint64_t& numLine, uint64_t& dealLine, uint64_t& readPos, LARGE_INTEGER& fileSize);

private:
	bool m_isExtra = false;
	bool m_showsp = false;
	HANDLE m_hConsole;
	WORD m_oriAttributes;
	uint64_t m_dealLine = 0;
	uint64_t m_speedLine = 0;
	std::string m_sysMesage;
	std::chrono::time_point<std::chrono::steady_clock> m_starTtime;
};

#endif