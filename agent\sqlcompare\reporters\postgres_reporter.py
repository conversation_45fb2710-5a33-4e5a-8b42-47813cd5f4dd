#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
# 使用统一的数据模型和服务
from models.sqlalchemy_models import DiffStatus
from services.sqlalchemy_service import SQLAlchemyComparisonService
from reporters.base_reporter import BaseReporter

# 配置日志
logger = logging.getLogger(__name__)


class PostgresReporter(BaseReporter):
    """PostgreSQL报告器"""

    def __init__(self, config: Dict[str, Any]):
        """初始化PostgreSQL报告器"""
        super().__init__(config)

        # 基础配置
        self.task_id = config.get('task_id') or f"task_{int(time.time())}"

        # PostgreSQL连接配置
        self.pg_config = {
            'host': config.get('host', 'localhost'),
            'port': config.get('port', 5432),
            'database': config.get('database', 'postgres'),
            'username': config.get('username', 'postgres'),
            'password': config.get('password', ''),
            'schema': config.get('schema', 'public')
        }

        # 性能配置
        self.append_mode = config.get('append_mode', False)
        self.batch_size = config.get('batch_size', 50000)
        self.use_copy = config.get('use_copy', True)
        self.high_performance_mode = config.get('high_performance_mode', True)
        self.commit_interval = config.get('commit_interval', 100000)

        # 增量持久化配置
        config['cache_threshold'] = config.get('cache_threshold', 10000)  # 缓存阈值

        # 构建PostgreSQL连接串
        database_url = (f"postgresql+psycopg2://{self.pg_config['username']}:{self.pg_config['password']}"
                       f"@{self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}")

        # 初始化SQLAlchemy服务（保留用于兼容性）
        self.service = SQLAlchemyComparisonService.get_instance(database_url)
        self.service.ensure_tables_initialized()

        # 性能统计
        self.stats = {
            'total_inserts': 0,
            'batch_commits': 0,
            'total_time': 0,
            'avg_batch_time': 0
        }

    def open(self):
        """打开报告目标"""
        try:
            # 清空差异收集列表
            self.diffs_list.clear()

        except Exception as e:
            logger.error(f"PostgreSQL报告器初始化失败: {e}")
            raise

    def close(self):
        """关闭报告目标"""
        try:
            # 确保所有缓存的数据都被写入数据库
            self.force_flush()

        except Exception as e:
            logger.error(f"关闭PostgreSQL报告器时出错: {e}")

    def report_diff(self, diff_result: Any, table_name: str = None):
        """记录一条差异"""
        try:
            # 使用BaseReporter的方法
            super().report_diff(diff_result, table_name)

            # 更新统计信息
            self.stats['total_inserts'] += 1

        except Exception as e:
            logger.error(f"报告差异失败: {e}")
            raise

    def get_comparison_results(
        self,
        table_name: str = None,
        diff_status: DiffStatus = None,
        status: str = None,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取比对结果 - 委托给SQLAlchemy服务

        Args:
            table_name: 表名过滤
            diff_status: 差异类型过滤
            status: 状态过滤
            limit: 返回数量限制
            offset: 偏移量

        Returns:
            (结果列表, 总记录数)
        """
        try:
            # 委托给SQLAlchemy服务进行查询
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import func

                # 构建查询
                query = session.query(ComparisonResult)

                # 添加过滤条件
                if table_name:
                    query = query.filter(ComparisonResult.table_name == table_name)
                if status:
                    query = query.filter(ComparisonResult.status == status)
                if self.task_id:
                    query = query.filter(ComparisonResult.task_id == self.task_id)

                # 获取总记录数
                total_count = query.count()

                # 获取分页数据
                results_query = query.order_by(ComparisonResult.id.desc()).offset(offset).limit(limit)
                results = []

                for result in results_query:
                    results.append({
                        'id': result.id,
                        'record_key': result.record_key,
                        'status': result.status,
                        'field_name': result.field_name,
                        'value_a': result.source_value,
                        'value_b': result.target_value,
                        'created_at': result.created_at.isoformat() if result.created_at else None
                    })

                return results, total_count

        except Exception as e:
            logger.error(f"查询比对结果失败: {e}")
            return [], 0

    def search_records(
        self,
        table_name: str,
        search_term: str,
        page: int = 1,
        page_size: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        搜索包含指定关键词的记录 - 委托给SQLAlchemy服务

        Args:
            table_name: 表名
            search_term: 搜索关键词
            page: 页码
            page_size: 每页记录数

        Returns:
            (结果列表, 总记录数)
        """
        try:
            # 委托给SQLAlchemy服务进行搜索
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import or_

                # 构建搜索查询
                search_pattern = f"%{search_term}%"
                query = session.query(ComparisonResult).filter(
                    ComparisonResult.table_name == table_name,
                    or_(
                        ComparisonResult.record_key.like(search_pattern),
                        ComparisonResult.source_value.like(search_pattern),
                        ComparisonResult.target_value.like(search_pattern),
                        ComparisonResult.field_name.like(search_pattern)
                    )
                )

                # 添加任务ID过滤
                if self.task_id:
                    query = query.filter(ComparisonResult.task_id == self.task_id)

                # 获取总记录数
                total_count = query.count()

                # 获取分页数据
                offset = (page - 1) * page_size
                results_query = query.order_by(ComparisonResult.id.desc()).offset(offset).limit(page_size)

                results = []
                for result in results_query:
                    results.append({
                        'id': result.id,
                        'record_key': result.record_key,
                        'status': result.status,
                        'field_name': result.field_name,
                        'value_a': result.source_value,
                        'value_b': result.target_value,
                        'created_at': result.created_at.isoformat() if result.created_at else None
                    })

                return results, total_count

        except Exception as e:
            logger.error(f"搜索记录失败: {e}")
            return [], 0

    def get_table_summary(self, table_name: str) -> Dict[str, int]:
        """
        获取表的差异统计摘要 - 委托给SQLAlchemy服务

        Args:
            table_name: 表名

        Returns:
            差异统计字典
        """
        try:
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import func

                # 构建查询
                query = session.query(
                    ComparisonResult.status,
                    func.count(ComparisonResult.id).label('count')
                ).filter(ComparisonResult.table_name == table_name)

                # 添加任务ID过滤
                if self.task_id:
                    query = query.filter(ComparisonResult.task_id == self.task_id)

                # 分组统计
                results = query.group_by(ComparisonResult.status).all()

                summary = {}
                for status, count in results:
                    summary[status] = count

                return summary

        except Exception as e:
            logger.error(f"获取表摘要失败: {e}")
            return {}

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            性能统计字典
        """
        batch_stats = self.batch_writer.get_stats() if hasattr(self, 'batch_writer') else {}

        return {
            'total_records': self.total_records,
            'batch_size': self.batch_size,
            'use_copy': self.use_copy,
            'high_performance_mode': self.high_performance_mode,
            'pg_config': {k: v for k, v in self.pg_config.items() if k != 'password'},  # 隐藏密码
            'table_name': self.batch_writer.table_name,
            'task_id': self.task_id,
            'stats': self.stats,
            'batch_writer_stats': batch_stats
        }

    def force_commit(self):
        """
        强制提交当前批处理数据
        用于确保数据及时写入数据库
        """
        if hasattr(self, 'batch_writer'):
            self.batch_writer.force_flush()
            logger.info(f"强制提交完成")

    def export_results_to_csv(self, output_path: str, table_name: str = None) -> bool:
        """
        导出结果到CSV文件

        Args:
            output_path: 输出文件路径
            table_name: 表名过滤

        Returns:
            是否导出成功
        """
        try:
            import csv

            # 获取所有结果数据
            results, _ = self.get_comparison_results(table_name=table_name, limit=1000000)

            if not results:
                return True

            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = results[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for result in results:
                    writer.writerow(result)

            logger.info(f"成功导出 {len(results)} 条记录到 {output_path}")
            return True

        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return False

    def create_indexes(self):
        """
        创建PostgreSQL优化索引
        提升查询性能
        """
        try:
            with self.service.get_db_session() as session:
                # 创建常用查询的索引
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_task_id ON comparison_results(task_id)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_table_name ON comparison_results(table_name)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_status ON comparison_results(status)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_record_key ON comparison_results(record_key)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_created_at ON comparison_results(created_at)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_composite ON comparison_results(task_id, table_name, status)"
                ]

                for index_sql in indexes:
                    try:
                        session.execute(index_sql)
                        logger.debug(f"创建索引: {index_sql}")
                    except Exception as e:
                        logger.warning(f"创建索引失败: {index_sql}, 错误: {e}")

                session.commit()
                logger.info("PostgreSQL索引创建完成")

        except Exception as e:
            logger.error(f"创建索引失败: {e}")

    def optimize_table(self):
        """
        优化PostgreSQL表
        执行VACUUM和ANALYZE
        """
        try:
            # 使用原生连接执行VACUUM（不能在事务中执行）
            if self.batch_writer.connection_pool:
                conn = self.batch_writer.connection_pool.getconn()
                try:
                    conn.autocommit = True
                    cursor = conn.cursor()
                    cursor.execute("VACUUM ANALYZE comparison_results")
                    logger.info("PostgreSQL表优化完成")
                finally:
                    conn.autocommit = False
                    self.batch_writer.connection_pool.putconn(conn)
            else:
                logger.warning("无法执行表优化：连接池不可用")

        except Exception as e:
            logger.error(f"表优化失败: {e}")

    # ==================== 任务管理API兼容性方法 ====================

    def create_comparison_task(self, name: str, description: str = None, **kwargs) -> Optional[str]:
        """
        创建新的比对任务（兼容性方法）

        Args:
            name: 任务名称
            description: 任务描述
            **kwargs: 其他任务参数

        Returns:
            任务ID
        """
        try:
            # 简化的任务创建逻辑（兼容性）
            task_id = f"task_{name}_{int(time.time())}"
            logger.info(f"创建比对任务: {name} (ID: {task_id})")
            logger.debug(f"任务描述: {description}")
            logger.debug(f"其他参数: {kwargs}")
            return task_id
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return None

    def start_task(self, task_id: str = None):
        """
        开始执行任务（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
        """
        task_id = task_id or self.task_id
        if task_id:
            logger.info(f"开始执行任务: {task_id}")
        else:
            logger.warning("未指定任务ID，无法启动任务")

    def complete_task(self, task_id: str = None, **summary):
        """
        完成任务（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
            **summary: 任务摘要信息
        """
        task_id = task_id or self.task_id
        if task_id:
            logger.info(f"完成任务: {task_id}, 摘要: {summary}")
        else:
            logger.warning("未指定任务ID，无法完成任务")

    def fail_task(self, task_id: str = None, error_msg: str = None):
        """
        标记任务失败（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
            error_msg: 错误信息
        """
        task_id = task_id or self.task_id
        if task_id:
            logger.error(f"任务失败: {task_id}, 错误: {error_msg}")
        else:
            logger.warning("未指定任务ID，无法标记任务失败")

    def update_task_progress(self, step_name: str, progress: float,
                           processed: int = 0, total: int = 0, message: str = None, task_id: str = None):
        """
        更新任务进度（兼容性方法）

        Args:
            step_name: 步骤名称
            progress: 进度百分比
            processed: 已处理记录数
            total: 总记录数
            message: 进度消息
            task_id: 任务ID，如果为None则使用当前任务ID
        """
        task_id = task_id or self.task_id
        if task_id:
            log_msg = f"任务进度更新: {task_id} - {step_name} - {progress}% ({processed}/{total})"
            if message:
                log_msg += f" - {message}"
            logger.debug(log_msg)
        else:
            logger.warning("未指定任务ID，无法更新任务进度")

    def get_task_info(self, task_id: str = None) -> Optional[Dict[str, Any]]:
        """
        获取任务信息（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID

        Returns:
            任务信息字典或None
        """
        task_id = task_id or self.task_id
        if task_id:
            # 返回基本的任务信息
            return {
                'task_id': task_id,
                'status': 'running',
                'total_records': self.total_records,
                'created_at': datetime.now().isoformat()
            }
        return None
