# cython: language_level=3
# cython: boundscheck=False
# cython: wraparound=False
# cython: initializedcheck=False
# cython: cdivision=True
# cython: embedsignature=True

"""
SQLCompare Cython优化引擎
==================================
高性能数据比对引擎，使用Cython优化关键路径
"""

import time
import math
import logging
from typing import Optional
try:
    from ..connectors.base_connector import BaseConnector
    from ..reporters.base_reporter import BaseReporter
    from ..models.sqlalchemy_models import DiffStatus
except ImportError:
    from connectors.base_connector import BaseConnector
    from reporters.base_reporter import BaseReporter
    from models.sqlalchemy_models import DiffStatus

# 设置日志
logger = logging.getLogger(__name__)

# 预编译常量优化
SOURCE_ONLY_STATUS = DiffStatus.SOURCE_ONLY.value
TARGET_ONLY_STATUS = DiffStatus.TARGET_ONLY.value
DIFFERENT_STATUS = DiffStatus.DIFFERENT.value

# 预分配字典模板优化
_diff_result_source_only = {
    'record_key': None,
    'status': SOURCE_ONLY_STATUS,
    'source_value': None,
    'target_value': None
}

_diff_result_target_only = {
    'record_key': None,
    'status': TARGET_ONLY_STATUS,
    'source_value': None,
    'target_value': None
}

_diff_result_different = {
    'record_key': None,
    'status': DIFFERENT_STATUS,
    'source_value': None,
    'target_value': None
}

cdef inline bint compare_values(object val_a, object val_b):
    """优化的值比较函数"""

    if val_a is val_b:
        return True
    if val_a is None or val_b is None:
        return False
    return val_a == val_b

cdef inline bint fast_compare_values(object val_a, object val_b):
    """内部使用的内联值比较函数"""

    # 身份比较 - 最快路径
    if val_a is val_b:
        return True

    # None检查 - 快速路径
    if val_a is None or val_b is None:
        return False

    # 类型检查优化 - 缓存type调用
    cdef object type_a = type(val_a)
    cdef object type_b = type(val_b)

    if type_a is type_b:
        # 相同类型的优化路径
        if type_a in (str, int, float, bool):
            # 基础类型直接比较，最常见的情况
            return val_a == val_b
        elif type_a is dict:
            # 字典比较优化
            if len(val_a) != len(val_b):
                return False
            if val_a.keys() != val_b.keys():
                return False
            # 递归比较字典值
            for k in val_a:
                if not fast_compare_values(val_a[k], val_b[k]):
                    return False
            return True
        else:
            # 其他类型
            return val_a == val_b

    # 类型不同时的字符串比较
    try:
        # 优化字符串转换
        if type_a is str:
            str_a = val_a
        else:
            str_a = str(val_a)

        if type_b is str:
            str_b = val_b
        else:
            str_b = str(val_b)

        return str_a == str_b
    except:
        return False

def compare_sources_stream_cython(source_a, source_b, reporter=None):
    """Cython流式归并比对算法"""

    # Cython类型声明
    cdef long source_only = 0
    cdef long target_only = 0
    cdef long diff_record = 0
    cdef long total_records = 0
    cdef long treated_count = 0
    
    # 性能计时
    cdef double start_time = time.perf_counter()
    cdef double lastlog_time = start_time
    
    # 预计算布尔值 - 避免重复检查
    has_reporter = reporter is not None
    has_progress_callback = has_reporter and hasattr(reporter, 'report_match')
    has_sqlalchemy_service = has_reporter and hasattr(reporter, '_sqlalchemy_service')
    
    # 动态调整进度报告间隔
    progress_interval = 100000 if has_reporter else 500000
    
    logger.info("开始Cython流式归并比对...")
       
    try:
        # 安全地打开资源
        if hasattr(source_a, '__enter__'):
            source_a.__enter__()
        if hasattr(source_b, '__enter__'):
            source_b.__enter__()     
        if has_reporter and hasattr(reporter, '__enter__'):
            reporter.__enter__()
        
        # 获取迭代器
        iter_a = iter(source_a)
        iter_b = iter(source_b)
        
        record_a = next(iter_a, None)
        record_b = next(iter_b, None)
        
        # 主比对循环
        while record_a is not None or record_b is not None:
            treated_count += 1
            
            # 进度报告
            if treated_count % progress_interval == 0:
                current_time = time.perf_counter()
                duration = current_time - lastlog_time
                rate = progress_interval / duration if duration > 0 else float('inf')
                
                logger.info("Cython已处理 %d 条记录，速率: %.0f 条/秒，差异: %d" %
                          (treated_count, rate, source_only + target_only + diff_record))
                
                if has_progress_callback:
                    reporter.report_match(f"progress_{treated_count}", treated_count, total_records)
                
                if has_sqlalchemy_service:
                    try:
                        if treated_count <= 100000:
                            progress_pct = 5.0 + (treated_count / 100000) * 25.0
                        elif treated_count <= 1000000:
                            progress_pct = 30.0 + ((treated_count - 100000) / 900000) * 30.0
                        else:
                            log_factor = math.log10(treated_count / 1000000 + 1)
                            progress_pct = min(95.0, 60.0 + log_factor * 35.0)
                        
                        reporter._sqlalchemy_service.update_task_progress(
                            reporter._task_id,
                            progress_pct,
                            "Cython流式比对 - 已处理 %d 条记录" % treated_count,
                            processed_records=treated_count
                        )
                    except Exception as e:
                        logger.debug("更新任务进度失败: %s" % str(e))
                
                lastlog_time = current_time
            
            # 核心比对逻辑
            if record_a and (record_b is None or record_a.key < record_b.key):
                source_only += 1
                if has_reporter:
                    _diff_result_source_only['record_key'] = record_a.key
                    _diff_result_source_only['source_value'] = record_a.value
                    _diff_result_source_only['target_value'] = None
                    reporter.report_diff(_diff_result_source_only)
                record_a = next(iter_a, None)
            
            elif record_b and (record_a is None or record_b.key < record_a.key):
                target_only += 1
                if has_reporter:
                    _diff_result_target_only['record_key'] = record_b.key
                    _diff_result_target_only['source_value'] = None
                    _diff_result_target_only['target_value'] = record_b.value
                    reporter.report_diff(_diff_result_target_only)
                record_b = next(iter_b, None)
            
            elif record_a and record_b:
                total_records += 2
                if not compare_values(record_a.value, record_b.value):
                    diff_record += 1
                    if has_reporter:
                        _diff_result_different['record_key'] = record_a.key
                        _diff_result_different['source_value'] = record_a.value
                        _diff_result_different['target_value'] = record_b.value
                        reporter.report_diff(_diff_result_different)
                
                record_a = next(iter_a, None)
                record_b = next(iter_b, None)

        # 完整的total_records计算
        total_records += source_only + target_only

    except Exception as e:
        logger.error("Cython比对过程中发生错误: %s" % str(e))
        raise
    
    finally:
        # 安全地关闭资源
        if has_reporter and hasattr(reporter, '__exit__'):
            try:
                reporter.__exit__(None, None, None)
            except Exception as e:
                logger.error("关闭reporter失败: %s" % str(e))
        
        if hasattr(source_b, '__exit__'):
            try:
                source_b.__exit__(None, None, None)
            except Exception as e:
                logger.error("关闭source_b失败: %s" % str(e))
        
        if hasattr(source_a, '__exit__'):
            try:
                source_a.__exit__(None, None, None)
            except Exception as e:
                logger.error("关闭source_a失败: %s" % str(e))
    
    # 计算统计信息
    cdef double exec_time = time.perf_counter() - start_time
    cdef double processing_rate = treated_count / exec_time if exec_time > 0 else 0
    
    logger.info("Cython数据比对完成耗时: %.2f秒，处理记录: %d条，速率: %.0f条/秒" %
              (exec_time, treated_count, processing_rate))
    logger.info("A源: %d条，B源: %d条，差异: %d条" %
              (source_only, target_only, source_only + target_only + diff_record))
    
    return {
        'total_records': total_records,
        'processed_records': treated_count,
        'diff_records': diff_record,
        'source_only': source_only,
        'target_only': target_only,
        'exec_time': exec_time,
        'processing_rate': processing_rate
    }

def compare_sources_memory_cython(source_a, source_b, reporter=None):
    """高性能Cython内存字典比对算法"""

    # Cython类型声明
    cdef long source_only = 0
    cdef long target_only = 0
    cdef long diff_record = 0
    cdef long total_records = 0
    cdef long treated_count = 0

    # 性能计时
    cdef double start_time = time.perf_counter()
    cdef double load_a_time = 0.0
    cdef double load_b_time = 0.0
    cdef double compare_time = 0.0

    # 预计算布尔值
    has_reporter = reporter is not None
    has_progress_callback = has_reporter and hasattr(reporter, 'report_match')
    has_sqlalchemy_service = has_reporter and hasattr(reporter, '_sqlalchemy_service')

    logger.info("开始Cython内存字典比对...")

    try:
        # 安全地打开资源
        if hasattr(source_a, '__enter__'):
            source_a.__enter__()

        if hasattr(source_b, '__enter__'):
            source_b.__enter__()

        if has_reporter and hasattr(reporter, '__enter__'):
            reporter.__enter__()

        # 阶段1：加载数据源A
        logger.info("Cython加载数据源A...")
        load_start = time.perf_counter()

        data_result = source_a.fetch_all_data(shared_dict=None, data_source='t1')
        source_a_count = len(data_result)

        load_a_time = time.perf_counter() - load_start
        logger.info("Cython数据源A加载完成: %d 条记录，耗时: %.2f秒" % (source_a_count, load_a_time))

        # 进度更新
        if has_sqlalchemy_service:
            try:
                reporter._sqlalchemy_service.update_task_progress(
                    reporter._task_id, 25.0,
                    "Cython数据源A加载完成 - %d 条记录" % source_a_count
                )
            except Exception as e:
                logger.debug("更新进度失败: %s" % str(e))

        # 阶段2：加载数据源B
        logger.info("Cython加载数据源B...")
        load_start = time.perf_counter()

        data_result = source_b.fetch_all_data(shared_dict=data_result, data_source='t2')
        source_b_count = sum(1 for item in data_result.values() if item.get('t2', '') != '')

        load_b_time = time.perf_counter() - load_start
        logger.info("Cython数据源B加载完成: %d 条记录，耗时: %.2f秒" % (source_b_count, load_b_time))

        # 阶段3：内存字典比对
        logger.info("Cython开始内存字典比对...")
        compare_start = time.perf_counter()

        for key, item in data_result.items():
            treated_count += 1

            source_value = item.get('t1', '')
            target_value = item.get('t2', '')

            if source_value and target_value:
                total_records += 2
                if not compare_values(source_value, target_value):
                    diff_record += 1
                    if has_reporter:
                        _diff_result_different['record_key'] = key
                        _diff_result_different['source_value'] = source_value
                        _diff_result_different['target_value'] = target_value
                        reporter.report_diff(_diff_result_different)
            elif source_value and not target_value:
                source_only += 1
                if has_reporter:
                    _diff_result_source_only['record_key'] = key
                    _diff_result_source_only['source_value'] = source_value
                    _diff_result_source_only['target_value'] = None
                    reporter.report_diff(_diff_result_source_only)
            elif target_value and not source_value:
                target_only += 1
                if has_reporter:
                    _diff_result_target_only['record_key'] = key
                    _diff_result_target_only['source_value'] = None
                    _diff_result_target_only['target_value'] = target_value
                    reporter.report_diff(_diff_result_target_only)

        compare_time = time.perf_counter() - compare_start

        # 完整的total_records计算
        total_records = diff_record + source_only + target_only

        logger.info("Cython内存字典比对完成，耗时: %.2f秒" % compare_time)

    except Exception as e:
        logger.error("Cython内存字典比对过程中发生错误: %s" % str(e))
        raise

    finally:
        # 安全地关闭资源
        if has_reporter and hasattr(reporter, '__exit__'):
            try:
                reporter.__exit__(None, None, None)
            except Exception as e:
                logger.error("关闭reporter失败: %s" % str(e))

        if hasattr(source_b, '__exit__'):
            try:
                source_b.__exit__(None, None, None)
            except Exception as e:
                logger.error("关闭source_b失败: %s" % str(e))

        if hasattr(source_a, '__exit__'):
            try:
                source_a.__exit__(None, None, None)
            except Exception as e:
                logger.error("关闭source_a失败: %s" % str(e))

    # 计算统计信息
    cdef double exec_time = time.perf_counter() - start_time
    cdef double processing_rate = treated_count / exec_time if exec_time > 0 else 0

    logger.info("Cython内存字典比对完成总耗时: %.2f秒" % exec_time)
    logger.info("处理记录: %d条，速率: %.0f条/秒" % (treated_count, processing_rate))

    return {
        'total_records': total_records,
        'processed_records': treated_count,
        'diff_records': diff_record,
        'source_only': source_only,
        'target_only': target_only,
        'exec_time': exec_time,
        'processing_rate': processing_rate,
        'load_a_time': load_a_time,
        'load_b_time': load_b_time,
        'compare_time': compare_time
    }
