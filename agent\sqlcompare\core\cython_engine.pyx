# cython: language_level=3
# cython: boundscheck=False
# cython: wraparound=False
# cython: cdivision=True
# cython: profile=False

"""
Cython优化的数据比对引擎扩展
专门用于加速数据读取和Record对象处理
"""

import cython
import time
import logging
from typing import Iterator, Optional
from libc.stdlib cimport malloc, free
from cpython.object cimport PyObject

# 导入基础类型
# 注意：在编译时这些导入可能不可用，所以我们在函数中处理类型
# from .cython_models cimport CythonRecord, compare_values_cython

# 运行时导入
from typing import Iterator, Optional

logger = logging.getLogger(__name__)

# 预分配全局字典模板 - 借鉴engine_cython.pyx的优化
diff_result_source_only = {
    'record_key': None,
    'status': 'SO',
    'source_value': None,
    'target_value': None
}

diff_result_target_only = {
    'record_key': None,
    'status': 'TO',
    'source_value': None,
    'target_value': None
}

diff_result_different = {
    'record_key': None,
    'status': 'DF',
    'source_value': None,
    'target_value': None
}

@cython.boundscheck(False)
@cython.wraparound(False)
def compare_sources_stream_cython(source_a, source_b, reporter=None, use_advanced_compare=False) -> dict:
    """
    Cython优化的流式归并比对引擎 - 增强版

    Args:
        source_a: 数据源A连接器
        source_b: 数据源B连接器
        reporter: 差异报告器
        use_advanced_compare: 是否使用高级值比较（支持递归字典/列表）

    Returns:
        比对统计结果字典
    """
    cdef int source_only = 0
    cdef int target_only = 0
    cdef int diff_record = 0
    cdef int total_records = 0
    cdef int treated_count = 0  # 添加处理计数器
    cdef double start_time = time.perf_counter()
    cdef double lastlog_time = start_time  # 添加进度跟踪时间
    cdef bint values_equal  # 预声明变量

    # 预计算布尔值 - 借鉴engine_cython.pyx的优化
    has_reporter = reporter is not None
    has_progress_callback = has_reporter and hasattr(reporter, 'report_match')

    # 动态调整进度报告间隔
    progress_interval = 100000 if has_reporter else 500000
    
    # 检查数据源状态并获取迭代器
    try:
        source_a.connect()
        source_b.connect()

        # 尝试获取迭代器
        iter_a = iter(source_a)
        iter_b = iter(source_b)
    except Exception as e:
        raise RuntimeError(f"数据源迭代器创建失败: {e}")
    
    # 初始化记录
    record_a = next(iter_a, None)
    record_b = next(iter_b, None)
    
    # 使用预分配的全局模板 - 性能优化
    # 不再在函数内部创建新字典，直接使用全局模板
    
    # 主比对循环 - Cython优化
    while record_a is not None or record_b is not None:
        treated_count += 1

        # 进度跟踪 - 借鉴engine_cython.pyx
        if treated_count % progress_interval == 0:
            current_time = time.perf_counter()
            duration = current_time - lastlog_time
            rate = progress_interval / duration if duration > 0 else float('inf')
            logger.info("已处理 %d 条记录，速率: %.0f 条/秒" % (treated_count, rate))
            lastlog_time = current_time
        if record_a is None:
            # 只有B有数据
            target_only += 1
            if reporter:
                diff_result_target_only['record_key'] = record_b.key
                diff_result_target_only['target_value'] = record_b.value
                reporter.report_diff(diff_result_target_only)
            record_b = next(iter_b, None)
            
        elif record_b is None:
            # 只有A有数据
            source_only += 1
            if reporter:
                diff_result_source_only['record_key'] = record_a.key
                diff_result_source_only['source_value'] = record_a.value
                reporter.report_diff(diff_result_source_only)
            record_a = next(iter_a, None)
            
        else:
            # 比较键值
            key_cmp = _compare_keys_cython(record_a.key, record_b.key)
            
            if key_cmp < 0:
                # A的键小于B的键
                source_only += 1
                if reporter:
                    diff_result_source_only['record_key'] = record_a.key
                    diff_result_source_only['source_value'] = record_a.value
                    reporter.report_diff(diff_result_source_only)
                record_a = next(iter_a, None)
                
            elif key_cmp > 0:
                # A的键大于B的键
                target_only += 1
                if reporter:
                    diff_result_target_only['record_key'] = record_b.key
                    diff_result_target_only['target_value'] = record_b.value
                    reporter.report_diff(diff_result_target_only)
                record_b = next(iter_b, None)
                
            else:
                # 键相同，比较值 - 支持高级比较模式
                if use_advanced_compare:
                    values_equal = _compare_values_advanced(record_a.value, record_b.value)
                else:
                    values_equal = _compare_values_simple(record_a.value, record_b.value)

                if not values_equal:
                    diff_record += 1
                    if reporter:
                        diff_result_different['record_key'] = record_a.key
                        diff_result_different['source_value'] = record_a.value
                        diff_result_different['target_value'] = record_b.value
                        reporter.report_diff(diff_result_different)

                total_records += 2
                record_a = next(iter_a, None)
                record_b = next(iter_b, None)
    
    # 计算总耗时和处理速率
    cdef double total_time = time.perf_counter() - start_time
    cdef double processing_rate = treated_count / total_time if total_time > 0 else 0

    # 详细日志 - 借鉴engine_cython.pyx
    logger.info("Cython流式比对完成耗时: %.2f秒，处理记录: %d条，速率: %.0f条/秒" %
              (total_time, treated_count, processing_rate))
    logger.info("源独有: %d条，目标独有: %d条，差异: %d条" %
              (source_only, target_only, diff_record))

    # 返回增强的统计结果
    return {
        'source_only': source_only,
        'target_only': target_only,
        'different': diff_record,
        'total_records': total_records,
        'processed_records': treated_count,  # 新增
        'total_time': total_time,
        'processing_rate': processing_rate,  # 新增
        'engine_type': 'cython_stream_enhanced'  # 标识增强版本
    }

@cython.boundscheck(False)
@cython.wraparound(False)
cdef inline int _compare_keys_cython(str key_a, str key_b):
    """Cython内联键比较函数"""
    if key_a < key_b:
        return -1
    elif key_a > key_b:
        return 1
    else:
        return 0

@cython.boundscheck(False)
@cython.wraparound(False)
cdef inline bint _compare_values_simple(object value_a, object value_b):
    """简化的Cython值比较函数"""
    # 身份比较 - 最快
    if value_a is value_b:
        return True

    # None检查
    if value_a is None or value_b is None:
        return False

    # 类型比较优化
    if type(value_a) is type(value_b):
        return value_a == value_b

    # 不同类型的字符串比较
    return str(value_a) == str(value_b)

@cython.boundscheck(False)
@cython.wraparound(False)
cdef inline bint _compare_values_advanced(object val_a, object val_b):
    """高级值比较函数 - 借鉴engine_cython.pyx，支持递归字典比较"""
    # 身份比较 - 最快路径
    if val_a is val_b:
        return True

    # None检查
    if val_a is None or val_b is None:
        return False

    # 获取类型
    cdef object type_a = type(val_a)
    cdef object type_b = type(val_b)

    # 类型不同时的处理
    if type_a is not type_b:
        return str(val_a) == str(val_b)

    # 字典类型的递归比较
    if type_a is dict:
        if len(val_a) != len(val_b):
            return False
        if val_a.keys() != val_b.keys():
            return False
        for k in val_a:
            if not _compare_values_advanced(val_a[k], val_b[k]):  # 递归比较
                return False
        return True

    # 列表类型的递归比较
    elif type_a is list:
        if len(val_a) != len(val_b):
            return False
        for i in range(len(val_a)):
            if not _compare_values_advanced(val_a[i], val_b[i]):  # 递归比较
                return False
        return True

    # 其他类型的直接比较
    else:
        return val_a == val_b

@cython.boundscheck(False)
@cython.wraparound(False)
def compare_sources_memory_cython(source_a, source_b, reporter=None) -> dict:
    """
    Cython优化的内存字典比对引擎
    
    Args:
        source_a: 数据源A连接器
        source_b: 数据源B连接器
        reporter: 差异报告器
        
    Returns:
        比对统计结果字典
    """
    cdef double start_time = time.perf_counter()
    cdef int source_only = 0
    cdef int target_only = 0
    cdef int diff_record = 0
    cdef int total_records = 0

    # 构建源A的字典 - 添加错误处理
    cdef dict dict_a = {}
    try:
        for record in source_a:
            dict_a[record.key] = record.value
    except Exception as e:
        raise RuntimeError(f"读取源A数据失败: {e}")

    # 构建源B的字典并进行比对 - 添加错误处理
    cdef dict dict_b = {}
    cdef set keys_b = set()

    try:
        for record in source_b:
            dict_b[record.key] = record.value
            keys_b.add(record.key)

            # 立即比对
            if record.key in dict_a:
                if not _compare_values_simple(dict_a[record.key], record.value):
                    diff_record += 1
                    if reporter:
                        diff_result = {
                            'record_key': record.key,
                            'status': 'DF',
                            'source_value': dict_a[record.key],
                            'target_value': record.value
                        }
                        reporter.report_diff(diff_result)
                total_records += 2
            else:
                target_only += 1
                if reporter:
                    diff_result = {
                        'record_key': record.key,
                        'status': 'TO',
                        'source_value': None,
                        'target_value': record.value
                    }
                    reporter.report_diff(diff_result)
    except Exception as e:
        raise RuntimeError(f"读取源B数据失败: {e}")
    
    # 查找只在A中存在的记录
    for key in dict_a:
        if key not in keys_b:
            source_only += 1
            if reporter:
                diff_result = {
                    'record_key': key,
                    'status': 'SO',
                    'source_value': dict_a[key],
                    'target_value': None
                }
                reporter.report_diff(diff_result)
    
    cdef double total_time = time.perf_counter() - start_time
    
    return {
        'source_only': source_only,
        'target_only': target_only,
        'different': diff_record,
        'total_records': total_records,
        'total_time': total_time,
        'engine_type': 'cython_memory'
    }

# Python包装器函数
def get_cython_engine_info():
    """获取增强版Cython引擎信息"""
    return {
        'available': True,
        'version': '2.0.0',  # 升级版本号
        'optimizations': [
            'stream_comparison',
            'memory_comparison',
            'record_creation',
            'value_comparison',
            'progress_tracking',      # 新增
            'advanced_value_compare', # 新增
            'global_template_reuse',  # 新增
            'recursive_dict_compare'  # 新增
        ],
        'enhancements': [
            '预分配全局字典模板',
            '动态进度跟踪系统',
            '高级递归值比较',
            '详细性能统计',
            '智能进度报告间隔'
        ]
    }
