# config/run_config.yaml

migrate_config: false

task:
  # from_config from_model
  type: 'from_model'
  model_id: 1
  user_name: 'system'
  task_name: 'Task from Model 1'

reporters:
  # 指定使用的报告器名称列表
  active: []

  # 定义所有可用的报告器配置
  configurations:
    sqlite:
      type: 'sqlite'
      db_path: 'reports/sqlcompare_v4.db'
      fallback_to_sqlite: false

    csv:
      type: 'csv'
      output_dir: 'reports/csv_exports/'
      fallback_to_sqlite: false

    postgres:
      type: 'postgresql'
      host: '************'
      port: 3389
      database: 'replayer'
      username: 'replayer'
      password: 'admin123'
      fallback_to_sqlite: true # 如果连接失败，回退到SQLite