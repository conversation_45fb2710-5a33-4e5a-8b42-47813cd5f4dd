"""
SQLCompare 混合比对引擎模块
========================
提供智能引擎选择和统一的比对接口，支持：
- 高性能Cython引擎（优先选择）
- 标准Python引擎（fallback）
- 自动引擎检测和降级
- 统一的API接口
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


def get_engine_info() -> Dict[str, Any]:
    """
    获取引擎信息

    Returns:
        Dict: 引擎信息字典
    """
    # 检测Cython引擎
    cython_available = False
    cython_error = None
    try:
        from core.engine_cython import compare_sources_stream_cython
        # 尝试调用函数以确保它真正可用
        cython_available = True
        logger.debug("Cython引擎检测成功")
    except (ImportError, ModuleNotFoundError) as e:
        cython_error = f"模块导入失败: {e}"
        logger.debug(f"Cython引擎不可用: {e}")
    except TypeError as e:
        cython_error = f"类型错误（可能是编译问题）: {e}"
        logger.debug(f"Cython引擎类型错误: {e}")
    except Exception as e:
        cython_error = f"未知异常: {type(e).__name__}: {e}"
        logger.debug(f"Cython引擎加载异常: {type(e).__name__}: {e}")

    # 检测Python引擎
    python_available = False
    python_error = None
    try:
        from core.engine import compare_sources_stream
        python_available = True
        logger.debug("Python引擎检测成功")
    except ImportError as e:
        python_error = str(e)
        logger.debug(f"Python引擎不可用: {e}")

    # 确定推荐引擎
    if cython_available:
        recommended_engine = 'cython'
    elif python_available:
        recommended_engine = 'python'
    else:
        recommended_engine = None

    return {
        'cython_available': cython_available,
        'python_available': python_available,
        'recommended_engine': recommended_engine,
        'cython_performance_boost': 60 if cython_available else 0,
        'cython_error': cython_error,
        'python_error': python_error
    }


def compare_sources_hybrid(source_a, source_b, reporter) -> Dict[str, Any]:
    """混合引擎流式归并比对函数"""

    try:
        from core.engine_cython import compare_sources_stream_cython
        return compare_sources_stream_cython(source_a, source_b, reporter)
    except (ImportError, ModuleNotFoundError) as e:
        error_msg = str(e)
        logger.info(f"Cython引擎模块导入失败: {error_msg}")
    except TypeError as e:
        logger.warning(f"Cython引擎类型错误: {e}")
    except Exception as e:
        logger.warning(f"Cython引擎异常 ({type(e).__name__}): {e}")

    # 降级到Python引擎
    try:
        from core.engine import compare_sources_stream
        logger.info("📝 使用Python流式归并算法")
        return compare_sources_stream(source_a, source_b, reporter)
    except ImportError as e:
        logger.error(f"Python引擎也不可用: {e}")
        raise RuntimeError("没有可用的比对引擎")


def compare_sources_memory_hybrid(source_a, source_b, reporter) -> Dict[str, Any]:
    """混合引擎内存字典比对函数"""

    try:
        from core.engine_cython import compare_sources_memory_cython
        return compare_sources_memory_cython(source_a, source_b, reporter)
    except (ImportError, ModuleNotFoundError) as e:
        error_msg = str(e)
        logger.info(f"Cython引擎模块导入失败: {error_msg}")
    except TypeError as e:
        logger.warning(f"Cython引擎类型错误: {e}")
    except Exception as e:
        logger.warning(f"Cython引擎异常 ({type(e).__name__}): {e}")

     # 降级到Python引擎
    try:
        from core.engine import compare_sources_memory
        logger.info("📝 使用Python内存字典算法")
        return compare_sources_memory(source_a, source_b, reporter)
    except ImportError as e:
        logger.error(f"Python引擎也不可用: {e}")
        raise RuntimeError("没有可用的比对引擎")


def get_hybrid_engine_status() -> Dict[str, Any]:
    """
    获取混合引擎状态信息

    Returns:
        Dict: 状态信息
    """
    engine_info = get_engine_info()

    return {
        'cython_available': engine_info.get('cython_available', False),
        'python_available': engine_info.get('python_available', False),
        'recommended_engine': engine_info.get('recommended_engine'),
        'cython_performance_boost': engine_info.get('cython_performance_boost', 0),
        'status': 'ready' if engine_info.get('recommended_engine') else 'no_engines'
    }